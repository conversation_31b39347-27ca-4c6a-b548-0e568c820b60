@echo off
chcp 65001 >nul
title نظام TYF Management System - البناء والتشغيل
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نظام TYF Management System                ║
echo ║                      البناء والتشغيل التلقائي                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص النظام...
echo.

REM التحقق من وجود .NET
echo 📋 التحقق من تثبيت .NET SDK...
dotnet --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ .NET SDK غير مثبت!
    echo.
    echo 📥 يرجى تحميل وتثبيت .NET SDK من:
    echo https://dotnet.microsoft.com/download
    echo.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('dotnet --version 2^>nul') do set DOTNET_VERSION=%%i
echo ✅ .NET SDK مثبت - الإصدار: %DOTNET_VERSION%
echo.

REM التحقق من وجود مجلد المشروع
echo 📁 التحقق من وجود مجلد المشروع...
if not exist "%~dp0TYFManagementSystem" (
    echo ❌ مجلد TYFManagementSystem غير موجود!
    echo.
    echo 📍 المسار المتوقع: %~dp0TYFManagementSystem
    echo.
    echo 📂 المحتويات الحالية:
    dir "%~dp0" /b
    echo.
    pause
    exit /b 1
)

echo ✅ مجلد المشروع موجود
echo.

REM التحقق من وجود ملف المشروع
if not exist "%~dp0TYFManagementSystem\TYFManagementSystem.csproj" (
    echo ❌ ملف المشروع غير موجود!
    echo.
    echo 📍 المسار المتوقع: %~dp0TYFManagementSystem\TYFManagementSystem.csproj
    echo.
    pause
    exit /b 1
)

echo ✅ ملف المشروع موجود
echo.

REM الانتقال إلى مجلد المشروع
echo 📂 الانتقال إلى مجلد المشروع...
cd /d "%~dp0TYFManagementSystem"
echo ✅ المسار الحالي: %CD%
echo.

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                         بدء البناء                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM تنظيف المشروع
echo 🧹 تنظيف المشروع...
dotnet clean --verbosity quiet
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تنظيف المشروع!
    pause
    exit /b 1
)
echo ✅ تم تنظيف المشروع بنجاح
echo.

REM استعادة الحزم
echo 📦 استعادة الحزم والتبعيات...
dotnet restore --verbosity quiet
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في استعادة الحزم!
    echo.
    echo 💡 جرب الأوامر التالية يدوياً:
    echo    dotnet restore --force
    echo    dotnet restore --no-cache
    echo.
    pause
    exit /b 1
)
echo ✅ تم استعادة الحزم بنجاح
echo.

REM بناء المشروع
echo 🏗️ بناء المشروع...
dotnet build --configuration Release --verbosity quiet
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المشروع!
    echo.
    echo 🔍 تشغيل البناء مع تفاصيل أكثر...
    dotnet build --configuration Release --verbosity normal
    echo.
    pause
    exit /b 1
)
echo ✅ تم بناء المشروع بنجاح!
echo.

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        تشغيل التطبيق                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 تشغيل نظام TYF Management System...
echo.
echo 💡 ملاحظات مهمة:
echo    • سيفتح التطبيق في نافذة منفصلة
echo    • لإصلاح مشكلة بيانات المواقع، استخدم "تشخيص قاعدة البيانات"
echo    • يمكنك إغلاق هذه النافذة بعد فتح التطبيق
echo.

timeout /t 3 /nobreak >nul

dotnet run --configuration Release

echo.
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تشغيل التطبيق!
    echo.
    echo 🔍 معلومات للمساعدة:
    echo    • تأكد من عدم تشغيل نسخة أخرى من التطبيق
    echo    • تحقق من توفر مساحة كافية على القرص الصلب
    echo    • جرب تشغيل الملف كمدير
    echo.
) else (
    echo ✅ تم إغلاق التطبيق بنجاح
)

echo.
echo 📋 انتهى التشغيل
pause
