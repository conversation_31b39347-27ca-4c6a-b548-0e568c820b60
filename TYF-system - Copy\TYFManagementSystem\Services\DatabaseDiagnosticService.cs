using Microsoft.EntityFrameworkCore;
using System.Text;
using TYFManagementSystem.Data;
using TYFManagementSystem.Models.Database;

namespace TYFManagementSystem.Services
{
    /// <summary>
    /// خدمة تشخيص وإصلاح مشاكل قاعدة البيانات
    /// </summary>
    public class DatabaseDiagnosticService : IDisposable
    {
        private readonly TyfDbContext _context;

        public DatabaseDiagnosticService()
        {
            _context = new TyfDbContext();
        }

        /// <summary>
        /// فحص وتشخيص مشكلة بيانات المواقع لمشروع معين
        /// </summary>
        public async Task<string> DiagnoseLocationDataIssueAsync(string projectId)
        {
            var report = new StringBuilder();
            report.AppendLine($"🔍 تقرير تشخيص بيانات المواقع للمشروع: {projectId}");
            report.AppendLine($"⏰ وقت الفحص: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine(new string('=', 60));

            try
            {
                // 1. فحص وجود المشروع في جدول IPTT
                var ipttProject = await _context.IpttProjects
                    .FirstOrDefaultAsync(p => p.ProjectId == projectId);

                if (ipttProject == null)
                {
                    report.AppendLine("❌ المشروع غير موجود في جدول IpttProjects");
                    return report.ToString();
                }

                report.AppendLine($"✅ المشروع موجود - ID: {ipttProject.Id}, Name: {ipttProject.ProjectName}");

                // 2. فحص المؤشرات
                var indicators = await _context.IpttIndicators
                    .Where(i => i.ProjectId == ipttProject.Id)
                    .OrderBy(i => i.IndicatorNumber)
                    .ToListAsync();

                report.AppendLine($"📊 عدد المؤشرات: {indicators.Count}");
                foreach (var indicator in indicators)
                {
                    report.AppendLine($"   - مؤشر {indicator.IndicatorNumber}: {indicator.IndicatorName}");
                }

                // 3. فحص أنواع البيانات
                var dataTypes = await _context.IpttDataTypes
                    .Include(dt => dt.Indicator)
                    .Where(dt => dt.Indicator.ProjectId == ipttProject.Id)
                    .ToListAsync();

                report.AppendLine($"📋 عدد أنواع البيانات: {dataTypes.Count}");
                foreach (var dataType in dataTypes)
                {
                    report.AppendLine($"   - {dataType.DataTypeName} (مؤشر {dataType.Indicator.IndicatorNumber})");
                }

                // 4. فحص الأعمدة الشهرية
                var monthColumns = await _context.IpttMonthColumns
                    .Where(mc => mc.ProjectId == ipttProject.Id)
                    .OrderBy(mc => mc.DisplayOrder)
                    .ToListAsync();

                report.AppendLine($"📅 عدد الأعمدة الشهرية: {monthColumns.Count}");
                foreach (var month in monthColumns)
                {
                    report.AppendLine($"   - {month.MonthColumn}");
                }

                // 5. فحص بيانات المواقع
                var locationData = await _context.IpttLocationData
                    .Include(ld => ld.Indicator)
                    .Include(ld => ld.DataType)
                    .Where(ld => ld.ProjectId == ipttProject.Id)
                    .OrderBy(ld => ld.LocationNumber)
                    .ThenBy(ld => ld.Indicator.IndicatorNumber)
                    .ThenBy(ld => ld.MonthColumn)
                    .ToListAsync();

                report.AppendLine($"🏢 عدد سجلات بيانات المواقع: {locationData.Count}");

                var locationGroups = locationData.GroupBy(ld => ld.LocationNumber);
                foreach (var locationGroup in locationGroups.OrderBy(g => g.Key))
                {
                    var locationNumber = locationGroup.Key;
                    var recordCount = locationGroup.Count();
                    report.AppendLine($"   📍 الموقع {locationNumber}: {recordCount} سجل");

                    // عرض عينة من البيانات
                    var sampleData = locationGroup.Take(5).ToList();
                    foreach (var sample in sampleData)
                    {
                        var indicatorName = sample.Indicator?.IndicatorNumber ?? "غير محدد";
                        var dataTypeName = sample.DataType?.DataTypeName ?? "غير محدد";
                        report.AppendLine($"      - مؤشر {indicatorName}, {dataTypeName}, {sample.MonthColumn}: '{sample.Value}'");
                    }

                    if (locationGroup.Count() > 5)
                    {
                        report.AppendLine($"      ... و {locationGroup.Count() - 5} سجل آخر");
                    }
                }

                // 6. فحص البيانات الشهرية القديمة (إن وجدت)
                var oldMonthlyData = await _context.IpttMonthlyData
                    .Include(md => md.Indicator)
                    .Include(md => md.DataType)
                    .Where(md => md.Indicator.ProjectId == ipttProject.Id)
                    .ToListAsync();

                if (oldMonthlyData.Any())
                {
                    report.AppendLine($"⚠️ توجد بيانات شهرية قديمة: {oldMonthlyData.Count} سجل");
                    report.AppendLine("   هذه البيانات قد تحتاج إلى ترحيل إلى جدول المواقع");
                }

                // 7. تحليل المشكلة
                report.AppendLine(new string('-', 40));
                report.AppendLine("🔍 تحليل المشكلة:");

                if (!locationData.Any())
                {
                    report.AppendLine("❌ لا توجد بيانات مواقع محفوظة");
                    if (oldMonthlyData.Any())
                    {
                        report.AppendLine("💡 يمكن ترحيل البيانات الشهرية القديمة إلى جدول المواقع");
                    }
                }
                else
                {
                    var maxLocationNumber = locationData.Max(ld => ld.LocationNumber);
                    report.AppendLine($"✅ توجد بيانات لـ {maxLocationNumber} موقع");

                    // فحص اكتمال البيانات
                    var expectedRecords = indicators.Count * dataTypes.Count * monthColumns.Count * maxLocationNumber;
                    var actualRecords = locationData.Count;
                    var completeness = (double)actualRecords / expectedRecords * 100;

                    report.AppendLine($"📈 اكتمال البيانات: {completeness:F1}% ({actualRecords}/{expectedRecords})");

                    if (completeness < 50)
                    {
                        report.AppendLine("⚠️ البيانات غير مكتملة - قد تحتاج إلى إعادة حفظ");
                    }
                }

            }
            catch (Exception ex)
            {
                report.AppendLine($"❌ خطأ في التشخيص: {ex.Message}");
            }

            return report.ToString();
        }

        /// <summary>
        /// إصلاح مشكلة بيانات المواقع عن طريق ترحيل البيانات القديمة
        /// </summary>
        public async Task<bool> FixLocationDataIssueAsync(string projectId)
        {
            try
            {
                var ipttProject = await _context.IpttProjects
                    .FirstOrDefaultAsync(p => p.ProjectId == projectId);

                if (ipttProject == null)
                    return false;

                // فحص وجود بيانات شهرية قديمة
                var oldMonthlyData = await _context.IpttMonthlyData
                    .Include(md => md.Indicator)
                    .Include(md => md.DataType)
                    .Where(md => md.Indicator.ProjectId == ipttProject.Id)
                    .ToListAsync();

                if (!oldMonthlyData.Any())
                    return false;

                // ترحيل البيانات إلى جدول المواقع
                var locationDataToAdd = new List<IpttLocationDataDb>();

                foreach (var oldData in oldMonthlyData)
                {
                    // التحقق من أن البيانات المطلوبة موجودة
                    if (oldData.IndicatorId.HasValue && oldData.DataTypeId.HasValue)
                    {
                        var locationData = new IpttLocationDataDb
                        {
                            ProjectId = ipttProject.Id,
                            LocationNumber = 1, // افتراض أن البيانات القديمة للموقع الأول
                            IndicatorId = oldData.IndicatorId.Value,
                            DataTypeId = oldData.DataTypeId.Value,
                            MonthColumn = oldData.MonthColumn,
                            Value = oldData.Value,
                            CreatedDate = oldData.CreatedDate,
                            LastModified = DateTime.Now
                        };

                        locationDataToAdd.Add(locationData);
                    }
                }

                // حفظ البيانات المرحلة
                _context.IpttLocationData.AddRange(locationDataToAdd);
                await _context.SaveChangesAsync();

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إنشاء بيانات تجريبية للاختبار
        /// </summary>
        public async Task<bool> CreateTestDataAsync(string projectId, int locationCount = 4)
        {
            try
            {
                var ipttProject = await _context.IpttProjects
                    .Include(p => p.Indicators)
                        .ThenInclude(i => i.DataTypes)
                    .Include(p => p.MonthColumns)
                    .FirstOrDefaultAsync(p => p.ProjectId == projectId);

                if (ipttProject == null)
                    return false;

                // حذف البيانات الموجودة
                var existingLocationData = _context.IpttLocationData
                    .Where(ld => ld.ProjectId == ipttProject.Id);
                _context.IpttLocationData.RemoveRange(existingLocationData);

                // إنشاء بيانات تجريبية
                var testData = new List<IpttLocationDataDb>();
                var random = new Random();

                for (int location = 1; location <= locationCount; location++)
                {
                    foreach (var indicator in ipttProject.Indicators)
                    {
                        foreach (var dataType in indicator.DataTypes)
                        {
                            foreach (var monthColumn in ipttProject.MonthColumns)
                            {
                                var value = random.Next(5, 25).ToString();
                                
                                var locationData = new IpttLocationDataDb
                                {
                                    ProjectId = ipttProject.Id,
                                    LocationNumber = location,
                                    IndicatorId = indicator.Id,
                                    DataTypeId = dataType.Id,
                                    MonthColumn = monthColumn.MonthColumn,
                                    Value = value,
                                    CreatedDate = DateTime.Now,
                                    LastModified = DateTime.Now
                                };

                                testData.Add(locationData);
                            }
                        }
                    }
                }

                _context.IpttLocationData.AddRange(testData);
                await _context.SaveChangesAsync();

                return true;
            }
            catch
            {
                return false;
            }
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
