using Microsoft.EntityFrameworkCore;
using System.Collections.ObjectModel;
using TYFManagementSystem.Data;
using TYFManagementSystem.Models;
using TYFManagementSystem.Models.Database;

namespace TYFManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة بيانات المشاريع في قاعدة البيانات
    /// </summary>
    public class ProjectDatabaseService
    {
        private readonly TyfDbContext _context;

        public ProjectDatabaseService()
        {
            _context = new TyfDbContext();
            InitializeDatabaseAsync().Wait();
        }

        private async Task InitializeDatabaseAsync()
        {
            try
            {
                await _context.EnsureDatabaseCreatedAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تهيئة قاعدة البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حفظ مشروع جديد في قاعدة البيانات
        /// </summary>
        public async Task<bool> AddProjectAsync(Project project)
        {
            try
            {
                var projectDb = ConvertToProjectDb(project);
                projectDb.CreatedDate = DateTime.Now;
                projectDb.LastModified = DateTime.Now;

                _context.Projects.Add(projectDb);
                await _context.SaveChangesAsync();

                // تحديث معرف المشروع
                project.Id = projectDb.Id;
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في إضافة المشروع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث مشروع موجود في قاعدة البيانات
        /// </summary>
        public async Task<bool> UpdateProjectAsync(Project project)
        {
            try
            {
                var existingProject = await _context.Projects.FindAsync(project.Id);
                if (existingProject == null)
                {
                    throw new Exception("المشروع غير موجود في قاعدة البيانات");
                }

                // تحديث البيانات
                existingProject.ProjectNumber = project.ProjectNumber;
                existingProject.ProjectCode = project.ProjectCode;
                existingProject.Name = project.Name;
                existingProject.Region = project.Region;
                existingProject.Description = project.Description;
                existingProject.StartDate = project.StartDate;
                existingProject.EndDate = project.EndDate;
                existingProject.Status = project.Status;
                existingProject.Budget = project.Budget;
                existingProject.Manager = project.Manager;
                existingProject.Beneficiaries = project.Beneficiaries;
                existingProject.LastModified = DateTime.Now;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تحديث المشروع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حذف مشروع من قاعدة البيانات
        /// </summary>
        public async Task<bool> DeleteProjectAsync(int projectId)
        {
            try
            {
                var project = await _context.Projects.FindAsync(projectId);
                if (project == null)
                {
                    return false;
                }

                _context.Projects.Remove(project);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في حذف المشروع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحميل جميع المشاريع من قاعدة البيانات
        /// </summary>
        public async Task<ObservableCollection<Project>> GetAllProjectsAsync()
        {
            try
            {
                var projectsDb = await _context.Projects
                    .OrderBy(p => p.Id)
                    .ToListAsync();

                var projects = new ObservableCollection<Project>();
                foreach (var projectDb in projectsDb)
                {
                    projects.Add(ConvertToProject(projectDb));
                }

                return projects;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تحميل المشاريع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// البحث عن مشروع بالمعرف
        /// </summary>
        public async Task<Project?> GetProjectByIdAsync(int id)
        {
            try
            {
                var projectDb = await _context.Projects.FindAsync(id);
                return projectDb != null ? ConvertToProject(projectDb) : null;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في البحث عن المشروع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// التحقق من وجود رقم مشروع
        /// </summary>
        public async Task<bool> ProjectNumberExistsAsync(string projectNumber, int excludeId = 0)
        {
            try
            {
                return await _context.Projects
                    .AnyAsync(p => p.ProjectNumber == projectNumber && p.Id != excludeId);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// الحصول على رقم مشروع جديد
        /// </summary>
        public async Task<string> GenerateProjectNumberAsync()
        {
            try
            {
                var year = DateTime.Now.Year;
                var lastProject = await _context.Projects
                    .Where(p => p.ProjectNumber.StartsWith($"{year}-TYF-"))
                    .OrderByDescending(p => p.ProjectNumber)
                    .FirstOrDefaultAsync();

                int nextNumber = 1;
                if (lastProject != null)
                {
                    var parts = lastProject.ProjectNumber.Split('-');
                    if (parts.Length == 3 && int.TryParse(parts[2], out int lastNumber))
                    {
                        nextNumber = lastNumber + 1;
                    }
                }

                return $"{year}-TYF-{nextNumber:D3}";
            }
            catch
            {
                return $"{DateTime.Now.Year}-TYF-001";
            }
        }

        /// <summary>
        /// تحويل من Project إلى ProjectDb
        /// </summary>
        private ProjectDb ConvertToProjectDb(Project project)
        {
            return new ProjectDb
            {
                Id = project.Id,
                ProjectNumber = project.ProjectNumber,
                ProjectCode = project.ProjectCode,
                Name = project.Name,
                Region = project.Region,
                Description = project.Description,
                StartDate = project.StartDate,
                EndDate = project.EndDate,
                Status = project.Status,
                Budget = project.Budget,
                Manager = project.Manager,
                Beneficiaries = project.Beneficiaries
            };
        }

        /// <summary>
        /// تحويل من ProjectDb إلى Project
        /// </summary>
        private Project ConvertToProject(ProjectDb projectDb)
        {
            return new Project
            {
                Id = projectDb.Id,
                ProjectNumber = projectDb.ProjectNumber,
                ProjectCode = projectDb.ProjectCode,
                Name = projectDb.Name,
                Region = projectDb.Region,
                Description = projectDb.Description,
                StartDate = projectDb.StartDate,
                EndDate = projectDb.EndDate,
                Status = projectDb.Status,
                Budget = projectDb.Budget,
                Manager = projectDb.Manager,
                Beneficiaries = projectDb.Beneficiaries
            };
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
