using System.Collections.ObjectModel;
using TYFManagementSystem.Models.IPTT;

namespace TYFManagementSystem.Services.IPTT
{
    /// <summary>
    /// خدمة حسابات IPTT - مسؤولة عن جميع العمليات الحسابية والإحصائية
    /// </summary>
    public class IpttCalculationService
    {
        /// <summary>
        /// حساب الإنجاز لموقع محدد
        /// </summary>
        public void CalculateAchievementForLocation(
            ObservableCollection<IpttDisplayRow> locationRows,
            List<string> monthColumns)
        {
            try
            {
                if (locationRows == null || !locationRows.Any())
                    return;

                // حساب الإنجاز لصفوف أنواع البيانات
                foreach (var row in locationRows.Where(r => r.IsDataTypeRow && !r.IsTotalRow))
                {
                    CalculateRowAchievement(row, monthColumns);
                }

                // حساب إجمالي المؤشرات الرئيسية
                CalculateMainIndicatorTotals(locationRows);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حساب الإنجاز: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حساب الإنجاز لصف واحد
        /// </summary>
        private void CalculateRowAchievement(IpttDisplayRow row, List<string> monthColumns)
        {
            try
            {
                // حساب الإنجاز من مجموع الأشهر
                double totalAchievement = 0;
                foreach (var month in monthColumns)
                {
                    if (row.MonthlyData.ContainsKey(month))
                    {
                        var monthValue = row.MonthlyData[month];
                        if (!string.IsNullOrWhiteSpace(monthValue) &&
                            double.TryParse(monthValue, out double value))
                        {
                            totalAchievement += value;
                        }
                    }
                }

                row.Achievement = totalAchievement.ToString("F0");

                // حساب النسبة المئوية
                if (!string.IsNullOrWhiteSpace(row.Target) &&
                    double.TryParse(row.Target, out double target) && target > 0)
                {
                    double percentage = (totalAchievement / target) * 100;
                    row.AchievementPercentage = $"{percentage:F1}%";
                    
                    // تحديد حالة الإكمال
                    row.IsCompleted = percentage >= 100;
                }
                else
                {
                    row.AchievementPercentage = "0.0%";
                    row.IsCompleted = false;
                }
            }
            catch (Exception ex)
            {
                // في حالة خطأ، تعيين قيم افتراضية
                row.Achievement = "0";
                row.AchievementPercentage = "0.0%";
                row.IsCompleted = false;
            }
        }

        /// <summary>
        /// حساب إجماليات المؤشرات الرئيسية
        /// </summary>
        private void CalculateMainIndicatorTotals(ObservableCollection<IpttDisplayRow> rows)
        {
            try
            {
                var indicatorGroups = rows.Where(r => r.IsDataTypeRow && !r.IsTotalRow)
                                         .GroupBy(r => r.IndicatorId);

                foreach (var group in indicatorGroups)
                {
                    var mainIndicatorRow = rows.FirstOrDefault(r => 
                        r.IsMainIndicator && r.IndicatorId == group.Key);

                    if (mainIndicatorRow != null)
                    {
                        CalculateMainIndicatorRow(mainIndicatorRow, group.ToList());
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حساب إجماليات المؤشرات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حساب صف المؤشر الرئيسي
        /// </summary>
        private void CalculateMainIndicatorRow(IpttDisplayRow mainRow, List<IpttDisplayRow> dataTypeRows)
        {
            try
            {
                double totalTarget = 0;
                double totalAchievement = 0;

                foreach (var dataTypeRow in dataTypeRows)
                {
                    // جمع الأهداف
                    if (double.TryParse(dataTypeRow.Target, out double target))
                    {
                        totalTarget += target;
                    }

                    // جمع الإنجازات
                    if (double.TryParse(dataTypeRow.Achievement, out double achievement))
                    {
                        totalAchievement += achievement;
                    }
                }

                mainRow.Target = totalTarget.ToString("F0");
                mainRow.Achievement = totalAchievement.ToString("F0");

                // حساب النسبة المئوية
                if (totalTarget > 0)
                {
                    double percentage = (totalAchievement / totalTarget) * 100;
                    mainRow.AchievementPercentage = $"{percentage:F1}%";
                    mainRow.IsCompleted = percentage >= 100;
                }
                else
                {
                    mainRow.AchievementPercentage = "0.0%";
                    mainRow.IsCompleted = false;
                }
            }
            catch (Exception ex)
            {
                // في حالة خطأ، تعيين قيم افتراضية
                mainRow.Target = "0";
                mainRow.Achievement = "0";
                mainRow.AchievementPercentage = "0.0%";
                mainRow.IsCompleted = false;
            }
        }

        /// <summary>
        /// حساب الإجمالي من جميع المواقع
        /// </summary>
        public void CalculateTotalFromAllLocations(
            ObservableCollection<IpttDisplayRow> totalRows,
            Dictionary<int, ObservableCollection<IpttDisplayRow>> locationData,
            List<string> monthColumns)
        {
            try
            {
                if (totalRows == null || !locationData.Any())
                    return;

                // حساب إجمالي كل صف من جميع المواقع
                foreach (var totalRow in totalRows.Where(r => r.IsDataTypeRow))
                {
                    CalculateTotalRowFromLocations(totalRow, locationData, monthColumns);
                }

                // حساب إجماليات المؤشرات الرئيسية
                CalculateMainIndicatorTotals(totalRows);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حساب الإجمالي من جميع المواقع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حساب صف الإجمالي من جميع المواقع
        /// </summary>
        private void CalculateTotalRowFromLocations(
            IpttDisplayRow totalRow,
            Dictionary<int, ObservableCollection<IpttDisplayRow>> locationData,
            List<string> monthColumns)
        {
            try
            {
                // إعادة تعيين البيانات الشهرية
                foreach (var month in monthColumns)
                {
                    totalRow.MonthlyData[month] = "0";
                }

                double totalTarget = 0;
                double totalAchievement = 0;

                // جمع البيانات من جميع المواقع (تجاهل الموقع 0 وهو الإجمالي)
                foreach (var locationKvp in locationData.Where(l => l.Key > 0))
                {
                    var matchingRow = locationKvp.Value.FirstOrDefault(r =>
                        r.IndicatorId == totalRow.IndicatorId &&
                        r.DataType == totalRow.DataType &&
                        r.IsDataTypeRow && !r.IsTotalRow);

                    if (matchingRow != null)
                    {
                        // جمع البيانات الشهرية
                        foreach (var month in monthColumns)
                        {
                            if (matchingRow.MonthlyData.ContainsKey(month) &&
                                double.TryParse(matchingRow.MonthlyData[month], out double monthValue))
                            {
                                if (double.TryParse(totalRow.MonthlyData[month], out double currentTotal))
                                {
                                    totalRow.MonthlyData[month] = (currentTotal + monthValue).ToString("F0");
                                }
                            }
                        }

                        // جمع الأهداف والإنجازات
                        if (double.TryParse(matchingRow.Target, out double target))
                        {
                            totalTarget += target;
                        }

                        if (double.TryParse(matchingRow.Achievement, out double achievement))
                        {
                            totalAchievement += achievement;
                        }
                    }
                }

                totalRow.Target = totalTarget.ToString("F0");
                totalRow.Achievement = totalAchievement.ToString("F0");

                // حساب النسبة المئوية الإجمالية
                if (totalTarget > 0)
                {
                    double percentage = (totalAchievement / totalTarget) * 100;
                    totalRow.AchievementPercentage = $"{percentage:F1}%";
                    totalRow.IsCompleted = percentage >= 100;
                }
                else
                {
                    totalRow.AchievementPercentage = "0.0%";
                    totalRow.IsCompleted = false;
                }
            }
            catch (Exception ex)
            {
                // في حالة خطأ، تعيين قيم افتراضية
                totalRow.Target = "0";
                totalRow.Achievement = "0";
                totalRow.AchievementPercentage = "0.0%";
                totalRow.IsCompleted = false;
            }
        }

        /// <summary>
        /// إنشاء ملخص الحسابات
        /// </summary>
        public string GenerateCalculationSummary(
            Dictionary<int, ObservableCollection<IpttDisplayRow>> locationData)
        {
            try
            {
                var summary = "📊 ملخص الحسابات:\n";
                summary += new string('=', 40) + "\n";

                foreach (var locationKvp in locationData.OrderBy(l => l.Key))
                {
                    var locationName = locationKvp.Key == 0 ? "الإجمالي" : $"الموقع {locationKvp.Key}";
                    summary += $"\n🏢 {locationName}:\n";

                    var dataTypeRows = locationKvp.Value.Where(r => r.IsDataTypeRow).ToList();
                    foreach (var row in dataTypeRows)
                    {
                        summary += $"   • {row.DataType}: الإنجاز = {row.Achievement}, النسبة = {row.AchievementPercentage}\n";
                    }
                }

                return summary;
            }
            catch (Exception ex)
            {
                return $"خطأ في إنشاء ملخص الحسابات: {ex.Message}";
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات قبل الحساب
        /// </summary>
        public bool ValidateDataForCalculation(ObservableCollection<IpttDisplayRow> rows)
        {
            try
            {
                if (rows == null || !rows.Any())
                    return false;

                // التحقق من وجود صفوف أنواع البيانات
                var dataTypeRows = rows.Where(r => r.IsDataTypeRow).ToList();
                if (!dataTypeRows.Any())
                    return false;

                // التحقق من وجود أهداف صحيحة
                foreach (var row in dataTypeRows)
                {
                    if (string.IsNullOrWhiteSpace(row.Target) ||
                        !double.TryParse(row.Target, out double target) ||
                        target < 0)
                    {
                        return false;
                    }
                }

                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
