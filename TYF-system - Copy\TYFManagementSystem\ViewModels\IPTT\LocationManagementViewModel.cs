using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using TYFManagementSystem.Commands;

namespace TYFManagementSystem.ViewModels.IPTT
{
    /// <summary>
    /// ViewModel لحوار إدارة المواقع
    /// </summary>
    public class LocationManagementViewModel : BaseViewModel
    {
        #region Private Fields
        private readonly EnhancedIpttViewModel _parentViewModel;
        private int _currentLocationCount;
        private int _newLocationCount;
        private bool _showPreview;
        private bool _hasDataLoss;
        private bool _canApplyChanges;
        private ObservableCollection<ChangeSummaryItem> _changesSummary;
        private ObservableCollection<DataLossItem> _dataLossDetails;
        #endregion

        #region Constructor
        public LocationManagementViewModel(EnhancedIpttViewModel parentViewModel)
        {
            _parentViewModel = parentViewModel ?? throw new ArgumentNullException(nameof(parentViewModel));
            _currentLocationCount = parentViewModel.TotalLocations;
            _newLocationCount = _currentLocationCount;
            _changesSummary = new ObservableCollection<ChangeSummaryItem>();
            _dataLossDetails = new ObservableCollection<DataLossItem>();

            InitializeCommands();
        }
        #endregion

        #region Properties
        public int CurrentLocationCount
        {
            get => _currentLocationCount;
            set => SetProperty(ref _currentLocationCount, value);
        }

        public int NewLocationCount
        {
            get => _newLocationCount;
            set
            {
                if (SetProperty(ref _newLocationCount, value))
                {
                    ValidateNewLocationCount();
                }
            }
        }

        public bool ShowPreview
        {
            get => _showPreview;
            set => SetProperty(ref _showPreview, value);
        }

        public bool HasDataLoss
        {
            get => _hasDataLoss;
            set => SetProperty(ref _hasDataLoss, value);
        }

        public bool CanApplyChanges
        {
            get => _canApplyChanges;
            set => SetProperty(ref _canApplyChanges, value);
        }

        public ObservableCollection<ChangeSummaryItem> ChangesSummary
        {
            get => _changesSummary;
            set => SetProperty(ref _changesSummary, value);
        }

        public ObservableCollection<DataLossItem> DataLossDetails
        {
            get => _dataLossDetails;
            set => SetProperty(ref _dataLossDetails, value);
        }
        #endregion

        #region Commands
        public ICommand PreviewChangesCommand { get; private set; }
        public ICommand ApplyChangesCommand { get; private set; }
        public ICommand CancelCommand { get; private set; }
        public ICommand CreateBackupCommand { get; private set; }
        #endregion

        #region Initialization
        private void InitializeCommands()
        {
            PreviewChangesCommand = new RelayCommand(PreviewChanges, CanPreviewChanges);
            ApplyChangesCommand = new RelayCommand(ApplyChanges, () => CanApplyChanges);
            CancelCommand = new RelayCommand(Cancel);
            CreateBackupCommand = new RelayCommand(CreateBackup);
        }
        #endregion

        #region Command Methods
        private bool CanPreviewChanges()
        {
            return NewLocationCount > 0 && NewLocationCount != CurrentLocationCount;
        }

        private void PreviewChanges()
        {
            try
            {
                ChangesSummary.Clear();
                DataLossDetails.Clear();
                HasDataLoss = false;

                if (NewLocationCount > CurrentLocationCount)
                {
                    // إضافة مواقع جديدة
                    var newLocations = NewLocationCount - CurrentLocationCount;
                    ChangesSummary.Add(new ChangeSummaryItem
                    {
                        Icon = "➕",
                        Description = $"سيتم إضافة {newLocations} موقع جديد",
                        Color = "#4CAF50"
                    });
                }
                else if (NewLocationCount < CurrentLocationCount)
                {
                    // حذف مواقع موجودة
                    var removedLocations = CurrentLocationCount - NewLocationCount;
                    ChangesSummary.Add(new ChangeSummaryItem
                    {
                        Icon = "➖",
                        Description = $"سيتم حذف {removedLocations} موقع",
                        Color = "#F44336"
                    });

                    // التحقق من فقدان البيانات
                    CheckForDataLoss();
                }

                ShowPreview = true;
                CanApplyChanges = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة التغييرات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CheckForDataLoss()
        {
            try
            {
                // فحص المواقع التي ستحذف
                for (int i = NewLocationCount + 1; i <= CurrentLocationCount; i++)
                {
                    var location = _parentViewModel.Locations.FirstOrDefault(l => l.LocationNumber == i);
                    if (location != null && location.Data.Any())
                    {
                        // فحص وجود بيانات
                        var hasData = location.Data.Any(row => 
                            row.MonthlyValues.Values.Any(value => !string.IsNullOrEmpty(value)));

                        if (hasData)
                        {
                            HasDataLoss = true;
                            var indicatorCount = location.Data.Count(row => row.IsIndicatorRow);
                            var dataCount = location.Data.Sum(row => 
                                row.MonthlyValues.Values.Count(value => !string.IsNullOrEmpty(value)));

                            DataLossDetails.Add(new DataLossItem
                            {
                                LocationName = location.Name,
                                DataSummary = $"{indicatorCount} مؤشر، {dataCount} قيمة مدخلة"
                            });
                        }
                    }
                }

                if (HasDataLoss)
                {
                    ChangesSummary.Add(new ChangeSummaryItem
                    {
                        Icon = "⚠️",
                        Description = "تحذير: ستفقد بيانات من المواقع المحذوفة",
                        Color = "#FF9800"
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في فحص فقدان البيانات: {ex.Message}");
            }
        }

        private void ApplyChanges()
        {
            try
            {
                if (HasDataLoss)
                {
                    var result = MessageBox.Show(
                        "تحذير: هذا التغيير سيؤدي إلى فقدان بيانات!\n\nهل أنت متأكد من المتابعة؟\n\nيُنصح بإنشاء نسخة احتياطية أولاً.",
                        "تأكيد فقدان البيانات",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result != MessageBoxResult.Yes)
                        return;
                }

                // إغلاق الحوار بنجاح
                if (Application.Current.MainWindow.OwnedWindows.OfType<Views.IPTT.LocationManagementDialog>().FirstOrDefault() is var dialog)
                {
                    dialog.DialogResult = true;
                    dialog.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق التغييرات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Cancel()
        {
            try
            {
                if (Application.Current.MainWindow.OwnedWindows.OfType<Views.IPTT.LocationManagementDialog>().FirstOrDefault() is var dialog)
                {
                    dialog.DialogResult = false;
                    dialog.Close();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إلغاء الحوار: {ex.Message}");
            }
        }

        private void CreateBackup()
        {
            try
            {
                // تطبيق إنشاء نسخة احتياطية
                MessageBox.Show("تم إنشاء نسخة احتياطية بنجاح!", "نسخة احتياطية", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        #endregion

        #region Private Methods
        private void ValidateNewLocationCount()
        {
            if (NewLocationCount < 1)
            {
                NewLocationCount = 1;
            }
            else if (NewLocationCount > 50) // حد أقصى معقول
            {
                NewLocationCount = 50;
                MessageBox.Show("الحد الأقصى لعدد المواقع هو 50", "تحذير", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
            }

            // إعادة تقييم إمكانية المعاينة
            ((RelayCommand)PreviewChangesCommand).RaiseCanExecuteChanged();
        }
        #endregion
    }

    #region Supporting Classes
    /// <summary>
    /// عنصر ملخص التغيير
    /// </summary>
    public class ChangeSummaryItem
    {
        public string Icon { get; set; } = "";
        public string Description { get; set; } = "";
        public string Color { get; set; } = "#000000";
    }

    /// <summary>
    /// عنصر تفاصيل فقدان البيانات
    /// </summary>
    public class DataLossItem
    {
        public string LocationName { get; set; } = "";
        public string DataSummary { get; set; } = "";
    }
    #endregion
}
