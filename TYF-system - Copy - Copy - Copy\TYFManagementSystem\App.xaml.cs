﻿using System.Configuration;
using System.Data;
using System.Windows;
using TYFManagementSystem.Services;

namespace TYFManagementSystem;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    protected override async void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        try
        {
            // تهيئة قاعدة البيانات عند بدء التطبيق
            await DatabaseInitializer.InitializeDatabaseAsync();
        }
        catch (Exception ex)
        {
            var result = MessageBox.Show(
                $"فشل في تهيئة قاعدة البيانات:\n{ex.Message}\n\nهل تريد إعادة تعيين قاعدة البيانات؟\n(سيتم حذف البيانات الموجودة وإنشاء قاعدة بيانات جديدة)",
                "خطأ في قاعدة البيانات",
                MessageBoxButton.YesNo,
                MessageBoxImage.Error);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    await DatabaseResetService.ResetDatabaseAsync();
                    MessageBox.Show("تم إعادة تعيين قاعدة البيانات بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception resetEx)
                {
                    MessageBox.Show($"فشل في إعادة تعيين قاعدة البيانات: {resetEx.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    Shutdown();
                }
            }
        }
    }
}

