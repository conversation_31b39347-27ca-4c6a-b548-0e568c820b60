using System.Windows;
using TYFManagementSystem.Services;

namespace TYFManagementSystem.Views
{
    /// <summary>
    /// نافذة تشخيص وإصلاح مشاكل قاعدة البيانات
    /// </summary>
    public partial class DatabaseDiagnosticWindow : Window
    {
        private DatabaseDiagnosticService diagnosticService;

        public DatabaseDiagnosticWindow()
        {
            InitializeComponent();
            diagnosticService = new DatabaseDiagnosticService();
        }

        private async void DiagnoseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SetBusyState(true, "جاري فحص قاعدة البيانات...");

                var projectId = ProjectIdTextBox.Text.Trim();
                if (string.IsNullOrEmpty(projectId))
                {
                    MessageBox.Show("يرجى إدخال معرف المشروع", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var report = await diagnosticService.DiagnoseLocationDataIssueAsync(projectId);
                ReportTextBox.Text = report;

                SetBusyState(false, "تم الانتهاء من الفحص");
            }
            catch (Exception ex)
            {
                SetBusyState(false, "خطأ في الفحص");
                MessageBox.Show($"خطأ في فحص قاعدة البيانات:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void FixButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var projectId = ProjectIdTextBox.Text.Trim();
                if (string.IsNullOrEmpty(projectId))
                {
                    MessageBox.Show("يرجى إدخال معرف المشروع", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show(
                    "هل أنت متأكد من أنك تريد إصلاح مشاكل قاعدة البيانات؟\n" +
                    "سيتم ترحيل البيانات القديمة إلى جدول المواقع الجديد.",
                    "تأكيد الإصلاح",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes)
                    return;

                SetBusyState(true, "جاري إصلاح قاعدة البيانات...");

                var success = await diagnosticService.FixLocationDataIssueAsync(projectId);

                if (success)
                {
                    SetBusyState(false, "تم الإصلاح بنجاح");
                    MessageBox.Show("تم إصلاح مشاكل قاعدة البيانات بنجاح!", "نجح الإصلاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    // إعادة فحص البيانات لإظهار النتائج
                    await Task.Delay(500);
                    DiagnoseButton_Click(sender, e);
                }
                else
                {
                    SetBusyState(false, "فشل الإصلاح");
                    MessageBox.Show("لم يتم العثور على بيانات قديمة لترحيلها.", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                SetBusyState(false, "خطأ في الإصلاح");
                MessageBox.Show($"خطأ في إصلاح قاعدة البيانات:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void CreateTestDataButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var projectId = ProjectIdTextBox.Text.Trim();
                if (string.IsNullOrEmpty(projectId))
                {
                    MessageBox.Show("يرجى إدخال معرف المشروع", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var result = MessageBox.Show(
                    "هل أنت متأكد من أنك تريد إنشاء بيانات تجريبية؟\n" +
                    "سيتم حذف البيانات الموجودة واستبدالها ببيانات تجريبية.",
                    "تأكيد إنشاء البيانات التجريبية",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result != MessageBoxResult.Yes)
                    return;

                SetBusyState(true, "جاري إنشاء البيانات التجريبية...");

                var success = await diagnosticService.CreateTestDataAsync(projectId, 4);

                if (success)
                {
                    SetBusyState(false, "تم إنشاء البيانات التجريبية");
                    MessageBox.Show("تم إنشاء البيانات التجريبية بنجاح!\nيمكنك الآن اختبار النظام.", "نجح الإنشاء", MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    // إعادة فحص البيانات لإظهار النتائج
                    await Task.Delay(500);
                    DiagnoseButton_Click(sender, e);
                }
                else
                {
                    SetBusyState(false, "فشل إنشاء البيانات");
                    MessageBox.Show("فشل في إنشاء البيانات التجريبية.\nتأكد من وجود المشروع في قاعدة البيانات.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                SetBusyState(false, "خطأ في إنشاء البيانات");
                MessageBox.Show($"خطأ في إنشاء البيانات التجريبية:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            ReportTextBox.Text = "اضغط على 'تشخيص' لبدء فحص قاعدة البيانات...";
            StatusTextBlock.Text = "جاهز للفحص";
        }

        private void SetBusyState(bool isBusy, string statusText)
        {
            DiagnoseButton.IsEnabled = !isBusy;
            FixButton.IsEnabled = !isBusy;
            CreateTestDataButton.IsEnabled = !isBusy;
            ProjectIdTextBox.IsEnabled = !isBusy;

            ProgressBar.Visibility = isBusy ? Visibility.Visible : Visibility.Collapsed;
            ProgressBar.IsIndeterminate = isBusy;

            StatusTextBlock.Text = statusText;

            if (isBusy)
            {
                Cursor = System.Windows.Input.Cursors.Wait;
            }
            else
            {
                Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            try
            {
                diagnosticService?.Dispose();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنظيف الموارد: {ex.Message}");
            }

            base.OnClosed(e);
        }
    }
}
