using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using TYFManagementSystem.Models;

namespace TYFManagementSystem.Services
{
    /// <summary>
    /// خدمة تصدير CSV بسيطة وموثوقة - بدون مشاكل المكتبات الخارجية
    /// </summary>
    public class CsvExportService
    {
        /// <summary>
        /// تصدير المشاريع إلى ملف CSV
        /// </summary>
        /// <param name="projects">قائمة المشاريع</param>
        /// <param name="filePath">مسار الملف</param>
        public void ExportProjectsToExcel(IEnumerable<Project> projects, string filePath)
        {
            // تغيير امتداد الملف إلى CSV
            var csvPath = Path.ChangeExtension(filePath, ".csv");
            ExportToCSV(projects, csvPath);
            
            // إعلام المستخدم بأن الملف تم حفظه كـ CSV
            throw new Exception($"تم حفظ البيانات كملف CSV: {csvPath}\n\nيمكنك فتح الملف في Excel أو أي برنامج جداول بيانات آخر.");
        }

        /// <summary>
        /// تصدير المشاريع إلى ملف CSV
        /// </summary>
        /// <param name="projects">قائمة المشاريع</param>
        /// <param name="filePath">مسار الملف</param>
        public void ExportToCSV(IEnumerable<Project> projects, string filePath)
        {
            var csv = new StringBuilder();
            
            // إضافة العنوان والتاريخ
            csv.AppendLine("مؤسسة تمدين شباب - تقرير المشاريع");
            csv.AppendLine($"تاريخ التصدير: {DateTime.Now:dd/MM/yyyy HH:mm}");
            csv.AppendLine();

            // إضافة عناوين الأعمدة
            csv.AppendLine("الرقم التلقائي,رقم المشروع,كود المشروع,اسم المشروع,منطقة المشروع,تاريخ البداية,تاريخ النهاية,الحالة,الميزانية,مدير المشروع,عدد المستفيدين,الوصف");

            // إضافة بيانات المشاريع
            var projectList = projects.ToList();
            foreach (var project in projectList)
            {
                var line = $"{project.Id}," +
                          $"\"{EscapeCsvValue(project.ProjectNumber)}\"," +
                          $"\"{EscapeCsvValue(project.ProjectCode)}\"," +
                          $"\"{EscapeCsvValue(project.Name)}\"," +
                          $"\"{EscapeCsvValue(project.Region)}\"," +
                          $"{project.StartDate:dd/MM/yyyy}," +
                          $"{project.EndDate:dd/MM/yyyy}," +
                          $"\"{EscapeCsvValue(project.Status)}\"," +
                          $"{project.Budget}," +
                          $"\"{EscapeCsvValue(project.Manager)}\"," +
                          $"{project.Beneficiaries}," +
                          $"\"{EscapeCsvValue(project.Description)}\"";
                
                csv.AppendLine(line);
            }

            // إضافة ملخص التقرير
            csv.AppendLine();
            csv.AppendLine("ملخص التقرير");
            csv.AppendLine($"إجمالي المشاريع:,{projectList.Count}");
            csv.AppendLine($"إجمالي الميزانية:,{projectList.Sum(p => p.Budget)}");
            csv.AppendLine($"إجمالي المستفيدين:,{projectList.Sum(p => p.Beneficiaries)}");

            // حفظ ملف CSV مع ترميز UTF-8 لدعم العربية
            File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
        }

        /// <summary>
        /// تنظيف قيم CSV من الأحرف الخاصة
        /// </summary>
        /// <param name="value">القيمة المراد تنظيفها</param>
        /// <returns>القيمة المنظفة</returns>
        private string EscapeCsvValue(string value)
        {
            if (string.IsNullOrEmpty(value))
                return "";

            // استبدال علامات الاقتباس المزدوجة
            return value.Replace("\"", "\"\"");
        }

        /// <summary>
        /// تصدير مباشر إلى CSV (للاستخدام المباشر)
        /// </summary>
        /// <param name="projects">قائمة المشاريع</param>
        /// <param name="filePath">مسار الملف</param>
        public void ExportProjectsToCSV(IEnumerable<Project> projects, string filePath)
        {
            ExportToCSV(projects, filePath);
        }
    }
}
