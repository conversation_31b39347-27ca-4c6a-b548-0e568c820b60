using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using TYFManagementSystem.Commands;
using TYFManagementSystem.Models;
using TYFManagementSystem.Models.IPTT;
using TYFManagementSystem.Services.IPTT;
using System.Windows;
using System.Windows.Threading;

namespace TYFManagementSystem.ViewModels.IPTT
{
    /// <summary>
    /// ViewModel محسن لنظام IPTT مع قائمة جانبية للمواقع
    /// </summary>
    public class EnhancedIpttViewModel : BaseViewModel
    {
        #region Private Fields
        private readonly IpttDatabaseService _databaseService;
        private readonly IpttDataManager _dataManager;
        private readonly IpttCalculationService _calculationService;
        private readonly AutoSaveService _autoSaveService;

        private Project _currentProject;
        private ObservableCollection<LocationItem> _locations;
        private LocationItem _selectedLocation;
        private ObservableCollection<IpttDisplayRow> _currentLocationData;
        private List<string> _monthColumns;
        private bool _isLoading;
        private string _statusMessage = "";
        private bool _hasUnsavedChanges;
        private int _totalLocations = 1;
        #endregion

        #region Constructor
        public EnhancedIpttViewModel(Project project)
        {
            _currentProject = project ?? throw new ArgumentNullException(nameof(project));
            _databaseService = new IpttDatabaseService();
            _dataManager = new IpttDataManager();
            _calculationService = new IpttCalculationService();
            _autoSaveService = new AutoSaveService();

            _locations = new ObservableCollection<LocationItem>();
            _currentLocationData = new ObservableCollection<IpttDisplayRow>();
            _monthColumns = new List<string>();

            InitializeCommands();
            InitializeAutoSave();
            LoadDataAsync();
        }
        #endregion

        #region Properties
        public Project CurrentProject
        {
            get => _currentProject;
            set => SetProperty(ref _currentProject, value);
        }

        public ObservableCollection<LocationItem> Locations
        {
            get => _locations;
            set => SetProperty(ref _locations, value);
        }

        public LocationItem SelectedLocation
        {
            get => _selectedLocation;
            set
            {
                if (SetProperty(ref _selectedLocation, value))
                {
                    LoadLocationDataAsync();
                }
            }
        }

        public ObservableCollection<IpttDisplayRow> CurrentLocationData
        {
            get => _currentLocationData;
            set => SetProperty(ref _currentLocationData, value);
        }

        public List<string> MonthColumns
        {
            get => _monthColumns;
            set => SetProperty(ref _monthColumns, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        public bool HasUnsavedChanges
        {
            get => _hasUnsavedChanges;
            set => SetProperty(ref _hasUnsavedChanges, value);
        }

        public int TotalLocations
        {
            get => _totalLocations;
            set
            {
                if (SetProperty(ref _totalLocations, value))
                {
                    UpdateLocations();
                }
            }
        }
        #endregion

        #region Commands
        public ICommand SaveCommand { get; private set; }
        public ICommand AddLocationCommand { get; private set; }
        public ICommand RemoveLocationCommand { get; private set; }
        public ICommand ManageLocationsCommand { get; private set; }
        public ICommand AddIndicatorCommand { get; private set; }
        public ICommand DeleteIndicatorCommand { get; private set; }
        public ICommand CalculateCommand { get; private set; }
        public ICommand ExportCommand { get; private set; }
        public ICommand RefreshCommand { get; private set; }
        #endregion

        #region Initialization
        private void InitializeCommands()
        {
            SaveCommand = new RelayCommand(async () => await SaveDataAsync());
            AddLocationCommand = new RelayCommand(AddLocationSafely);
            RemoveLocationCommand = new RelayCommand(RemoveLocationSafely);
            ManageLocationsCommand = new RelayCommand(ManageLocations);
            AddIndicatorCommand = new RelayCommand(AddIndicator);
            DeleteIndicatorCommand = new RelayCommand(DeleteIndicator);
            CalculateCommand = new RelayCommand(Calculate);
            ExportCommand = new RelayCommand(async () => await ExportDataAsync());
            RefreshCommand = new RelayCommand(async () => await LoadDataAsync());
        }

        private void InitializeAutoSave()
        {
            // إعداد أحداث خدمة الحفظ التلقائي
            _autoSaveService.AutoSaveCompleted += OnAutoSaveCompleted;
            _autoSaveService.AutoSaveFailed += OnAutoSaveFailed;

            // تعيين فترة الحفظ التلقائي (دقيقة واحدة)
            _autoSaveService.SetAutoSaveInterval(TimeSpan.FromMinutes(1));
        }
        #endregion

        #region Data Operations
        private async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "جاري تحميل البيانات...";

                var loadedData = await _dataManager.LoadProjectDataAsync(CurrentProject.Id);
                if (loadedData != null)
                {
                    TotalLocations = loadedData.LocationCount;
                    MonthColumns = loadedData.MonthColumns;
                    
                    // تحديث المواقع
                    UpdateLocations();
                    
                    // تحميل بيانات كل موقع
                    foreach (var location in Locations)
                    {
                        if (loadedData.LocationData.ContainsKey(location.LocationNumber))
                        {
                            location.Data = loadedData.LocationData[location.LocationNumber];
                            location.IsDataLoaded = true;
                        }
                    }

                    // اختيار الموقع الأول
                    if (Locations.Any())
                    {
                        SelectedLocation = Locations.First();
                    }

                    HasUnsavedChanges = false;
                    StatusMessage = "تم تحميل البيانات بنجاح";
                }
                else
                {
                    // إنشاء بيانات جديدة
                    InitializeNewProject();
                    StatusMessage = "تم إنشاء مشروع جديد";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تحميل البيانات: {ex.Message}";
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void InitializeNewProject()
        {
            // إنشاء أعمدة الأشهر الافتراضية
            MonthColumns = GenerateDefaultMonthColumns();
            
            // إنشاء المواقع
            UpdateLocations();
            
            // اختيار الموقع الأول
            if (Locations.Any())
            {
                SelectedLocation = Locations.First();
            }
        }

        private List<string> GenerateDefaultMonthColumns()
        {
            var columns = new List<string>();
            var startDate = CurrentProject.StartDate;
            var endDate = CurrentProject.EndDate;
            
            var current = new DateTime(startDate.Year, startDate.Month, 1);
            while (current <= endDate)
            {
                columns.Add(current.ToString("yyyy-MM"));
                current = current.AddMonths(1);
            }
            
            return columns;
        }

        private void UpdateLocations()
        {
            var currentSelection = SelectedLocation?.LocationNumber ?? 1;
            
            Locations.Clear();
            for (int i = 1; i <= TotalLocations; i++)
            {
                var location = new LocationItem
                {
                    LocationNumber = i,
                    Name = $"الموقع {i}",
                    Data = new ObservableCollection<IpttDisplayRow>(),
                    IsDataLoaded = false
                };
                
                Locations.Add(location);
            }
            
            // استعادة الاختيار السابق إن أمكن
            var locationToSelect = Locations.FirstOrDefault(l => l.LocationNumber == currentSelection) 
                                 ?? Locations.FirstOrDefault();
            
            if (locationToSelect != null)
            {
                SelectedLocation = locationToSelect;
            }
        }

        private async Task LoadLocationDataAsync()
        {
            if (SelectedLocation == null) return;

            try
            {
                if (!SelectedLocation.IsDataLoaded)
                {
                    StatusMessage = $"جاري تحميل بيانات {SelectedLocation.Name}...";
                    
                    // تحميل البيانات من قاعدة البيانات أو إنشاء بيانات جديدة
                    if (SelectedLocation.Data.Count == 0)
                    {
                        // إنشاء بيانات افتراضية للموقع
                        CreateDefaultLocationData(SelectedLocation);
                    }
                    
                    SelectedLocation.IsDataLoaded = true;
                }

                CurrentLocationData = SelectedLocation.Data;
                StatusMessage = $"تم تحميل بيانات {SelectedLocation.Name}";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تحميل بيانات الموقع: {ex.Message}";
            }
        }

        private void CreateDefaultLocationData(LocationItem location)
        {
            // إنشاء مؤشرات افتراضية
            var defaultIndicators = new[]
            {
                "عدد المستفيدين المباشرين",
                "عدد الأنشطة المنفذة", 
                "نسبة تحقيق الأهداف",
                "مستوى رضا المستفيدين"
            };

            for (int i = 0; i < defaultIndicators.Length; i++)
            {
                var row = new IpttDisplayRow
                {
                    No = (i + 1).ToString(),
                    Indicator = defaultIndicators[i],
                    DataType = "عدد",
                    Target = "100",
                    IsIndicatorRow = true
                };

                // إضافة القيم الشهرية الفارغة
                foreach (var month in MonthColumns)
                {
                    row.MonthlyValues[month] = "";
                }

                location.Data.Add(row);
            }
        }

        private async Task SaveDataAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "جاري حفظ البيانات...";

                // حفظ بيانات الموقع الحالي
                if (SelectedLocation != null)
                {
                    await SaveLocationDataAsync(SelectedLocation);
                }

                // حفظ جميع المواقع المحملة
                foreach (var location in Locations.Where(l => l.IsDataLoaded && l.HasUnsavedChanges))
                {
                    await SaveLocationDataAsync(location);
                }

                HasUnsavedChanges = false;
                StatusMessage = "تم حفظ البيانات بنجاح";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في حفظ البيانات: {ex.Message}";
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task SaveLocationDataAsync(LocationItem location)
        {
            // تحويل بيانات الموقع إلى تنسيق قاعدة البيانات وحفظها
            var indicators = ConvertDisplayRowsToIndicators(location.Data);
            var locationData = new Dictionary<int, ObservableCollection<IpttDisplayRow>>
            {
                { location.LocationNumber, location.Data }
            };

            await _databaseService.SaveIpttDataAsync(CurrentProject, indicators, MonthColumns, locationData);
            location.HasUnsavedChanges = false;
        }

        private ObservableCollection<IpttIndicator> ConvertDisplayRowsToIndicators(ObservableCollection<IpttDisplayRow> rows)
        {
            var indicators = new ObservableCollection<IpttIndicator>();

            foreach (var row in rows.Where(r => r.IsIndicatorRow))
            {
                var indicator = new IpttIndicator
                {
                    No = row.No,
                    Indicator = row.Indicator,
                    DataType = row.DataType,
                    Target = row.Target,
                    MonthlyValues = new Dictionary<string, string>(row.MonthlyValues)
                };

                indicators.Add(indicator);
            }

            return indicators;
        }

        private async Task AutoSaveAsync()
        {
            if (HasUnsavedChanges && SelectedLocation != null)
            {
                try
                {
                    await SaveLocationDataAsync(SelectedLocation);
                    StatusMessage = "تم الحفظ التلقائي";
                }
                catch
                {
                    // تجاهل أخطاء الحفظ التلقائي
                }
            }
        }

        private void AddIndicator()
        {
            if (SelectedLocation == null) return;

            var newRow = new IpttDisplayRow
            {
                No = (SelectedLocation.Data.Count + 1).ToString(),
                Indicator = "مؤشر جديد",
                DataType = "عدد",
                Target = "0",
                IsIndicatorRow = true
            };

            // إضافة القيم الشهرية الفارغة
            foreach (var month in MonthColumns)
            {
                newRow.MonthlyValues[month] = "";
            }

            SelectedLocation.Data.Add(newRow);
            SelectedLocation.HasUnsavedChanges = true;
            HasUnsavedChanges = true;
        }

        private void DeleteIndicator(object parameter)
        {
            if (parameter is IpttDisplayRow row && SelectedLocation != null)
            {
                var result = MessageBox.Show("هل أنت متأكد من حذف هذا المؤشر؟", "تأكيد الحذف",
                                           MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    SelectedLocation.Data.Remove(row);
                    SelectedLocation.HasUnsavedChanges = true;
                    HasUnsavedChanges = true;

                    // إعادة ترقيم المؤشرات
                    for (int i = 0; i < SelectedLocation.Data.Count; i++)
                    {
                        SelectedLocation.Data[i].No = (i + 1).ToString();
                    }
                }
            }
        }

        private void Calculate()
        {
            if (SelectedLocation == null) return;

            try
            {
                StatusMessage = "جاري حساب الإنجاز...";
                _calculationService.CalculateAchievementForLocation(SelectedLocation.Data, MonthColumns);
                StatusMessage = "تم حساب الإنجاز بنجاح";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في الحساب: {ex.Message}";
            }
        }

        private async Task ExportDataAsync()
        {
            try
            {
                StatusMessage = "جاري تصدير البيانات...";
                // تطبيق تصدير البيانات
                StatusMessage = "تم تصدير البيانات بنجاح";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في التصدير: {ex.Message}";
            }
        }

        public void OnCellValueChanged(IpttDisplayRow row, string columnName, string newValue)
        {
            // تحديث القيمة
            if (row.MonthlyValues.ContainsKey(columnName))
            {
                row.MonthlyValues[columnName] = newValue;
            }

            // تحديد أن هناك تغييرات غير محفوظة
            if (SelectedLocation != null)
            {
                SelectedLocation.HasUnsavedChanges = true;
            }
            HasUnsavedChanges = true;

            // حفظ فوري باستخدام خدمة الحفظ التلقائي
            Task.Run(async () =>
            {
                await _autoSaveService.SaveCellChangeAsync(CurrentProject, SelectedLocation.LocationNumber,
                                                         row.No, columnName, newValue);
            });
        }

        private void OnAutoSaveCompleted(object sender, AutoSaveEventArgs e)
        {
            StatusMessage = $"تم الحفظ التلقائي - {e.Message}";

            // تحديث حالة الحفظ للموقع
            if (SelectedLocation != null && e.SaveType != SaveType.FullProject)
            {
                SelectedLocation.HasUnsavedChanges = false;
            }

            if (e.SaveType == SaveType.FullProject)
            {
                HasUnsavedChanges = false;
                foreach (var location in Locations)
                {
                    location.HasUnsavedChanges = false;
                }
            }
        }

        private void OnAutoSaveFailed(object sender, AutoSaveEventArgs e)
        {
            StatusMessage = $"فشل في الحفظ التلقائي - {e.ErrorMessage}";
        }

        #region Safe Location Management
        private void AddLocationSafely()
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من إضافة موقع جديد؟\n\nسيتم إنشاء موقع فارغ جديد.",
                "تأكيد إضافة موقع",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                TotalLocations++;
                StatusMessage = $"تم إضافة موقع جديد. العدد الحالي: {TotalLocations}";
            }
        }

        private void RemoveLocationSafely()
        {
            if (TotalLocations <= 1)
            {
                MessageBox.Show("لا يمكن حذف الموقع الوحيد المتبقي.", "تحذير",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // التحقق من وجود بيانات في الموقع الأخير
            var lastLocation = Locations.LastOrDefault();
            bool hasData = lastLocation?.Data?.Any(row =>
                row.MonthlyValues.Values.Any(value => !string.IsNullOrEmpty(value))) ?? false;

            string message = hasData
                ? $"تحذير: الموقع الأخير ({lastLocation.Name}) يحتوي على بيانات!\n\nهل أنت متأكد من حذفه؟ ستفقد جميع البيانات المدخلة."
                : $"هل أنت متأكد من حذف الموقع الأخير؟";

            var icon = hasData ? MessageBoxImage.Warning : MessageBoxImage.Question;

            var result = MessageBox.Show(message, "تأكيد حذف موقع", MessageBoxButton.YesNo, icon);

            if (result == MessageBoxResult.Yes)
            {
                TotalLocations--;
                StatusMessage = $"تم حذف موقع. العدد الحالي: {TotalLocations}";
            }
        }

        private void ManageLocations()
        {
            try
            {
                var dialog = new Views.IPTT.LocationManagementDialog();
                var viewModel = new LocationManagementViewModel(this);
                dialog.DataContext = viewModel;
                dialog.Owner = Application.Current.MainWindow;

                if (dialog.ShowDialog() == true)
                {
                    // تطبيق التغييرات
                    if (viewModel.NewLocationCount != TotalLocations)
                    {
                        TotalLocations = viewModel.NewLocationCount;
                        StatusMessage = $"تم تحديث عدد المواقع إلى {TotalLocations}";
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح حوار إدارة المواقع: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        #endregion

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _autoSaveService?.Dispose();
            }
            base.Dispose(disposing);
        }
        #endregion
    }

    /// <summary>
    /// عنصر موقع في القائمة الجانبية
    /// </summary>
    public class LocationItem : INotifyPropertyChanged
    {
        private string _name = "";
        private bool _isDataLoaded;
        private bool _hasUnsavedChanges;

        public int LocationNumber { get; set; }
        
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public ObservableCollection<IpttDisplayRow> Data { get; set; } = new();

        public bool IsDataLoaded
        {
            get => _isDataLoaded;
            set => SetProperty(ref _isDataLoaded, value);
        }

        public bool HasUnsavedChanges
        {
            get => _hasUnsavedChanges;
            set => SetProperty(ref _hasUnsavedChanges, value);
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected virtual bool SetProperty<T>(ref T storage, T value, [System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            if (Equals(storage, value)) return false;
            storage = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}
