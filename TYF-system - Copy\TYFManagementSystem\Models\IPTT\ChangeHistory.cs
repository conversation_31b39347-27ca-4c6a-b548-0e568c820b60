using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TYFManagementSystem.Models.IPTT
{
    /// <summary>
    /// نموذج تتبع التغييرات في بيانات IPTT
    /// </summary>
    [Table("IpttChangeHistory")]
    public class IpttChangeHistory
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int ProjectId { get; set; }

        [Required]
        public int LocationNumber { get; set; }

        [Required]
        [MaxLength(50)]
        public string IndicatorNo { get; set; } = "";

        [Required]
        [MaxLength(20)]
        public string MonthColumn { get; set; } = "";

        [MaxLength(500)]
        public string OldValue { get; set; } = "";

        [MaxLength(500)]
        public string NewValue { get; set; } = "";

        [Required]
        public DateTime ChangeDate { get; set; }

        [MaxLength(100)]
        public string ChangedBy { get; set; } = "System";

        [MaxLength(50)]
        public string ChangeType { get; set; } = "Update";

        [MaxLength(1000)]
        public string Notes { get; set; } = "";

        // Navigation properties
        public virtual Project Project { get; set; }
    }

    /// <summary>
    /// نموذج إصدار من بيانات IPTT
    /// </summary>
    [Table("IpttVersions")]
    public class IpttVersion
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int ProjectId { get; set; }

        [Required]
        public int VersionNumber { get; set; }

        [Required]
        [MaxLength(100)]
        public string VersionName { get; set; } = "";

        [MaxLength(500)]
        public string Description { get; set; } = "";

        [Required]
        public DateTime CreatedDate { get; set; }

        [MaxLength(100)]
        public string CreatedBy { get; set; } = "System";

        [Required]
        public bool IsActive { get; set; } = true;

        // JSON serialized data
        [Column(TypeName = "TEXT")]
        public string SerializedData { get; set; } = "";

        // Navigation properties
        public virtual Project Project { get; set; }
    }

    /// <summary>
    /// نموذج عرض تاريخ التغييرات
    /// </summary>
    public class ChangeHistoryItem
    {
        public int Id { get; set; }
        public DateTime ChangeDate { get; set; }
        public string LocationName { get; set; } = "";
        public string IndicatorName { get; set; } = "";
        public string MonthColumn { get; set; } = "";
        public string OldValue { get; set; } = "";
        public string NewValue { get; set; } = "";
        public string ChangedBy { get; set; } = "";
        public string ChangeType { get; set; } = "";
        public string Notes { get; set; } = "";
        public string FormattedChangeDate => ChangeDate.ToString("dd/MM/yyyy HH:mm");
        public string ChangeDescription => $"تم تغيير {IndicatorName} في {MonthColumn} من '{OldValue}' إلى '{NewValue}'";
    }

    /// <summary>
    /// نموذج عرض الإصدارات
    /// </summary>
    public class VersionItem
    {
        public int Id { get; set; }
        public int VersionNumber { get; set; }
        public string VersionName { get; set; } = "";
        public string Description { get; set; } = "";
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; } = "";
        public bool IsActive { get; set; }
        public string FormattedCreatedDate => CreatedDate.ToString("dd/MM/yyyy HH:mm");
        public string StatusText => IsActive ? "نشط" : "غير نشط";
        public string StatusColor => IsActive ? "#4CAF50" : "#9E9E9E";
    }

    /// <summary>
    /// أنواع التغييرات
    /// </summary>
    public static class ChangeTypes
    {
        public const string Update = "Update";
        public const string Insert = "Insert";
        public const string Delete = "Delete";
        public const string BulkUpdate = "BulkUpdate";
        public const string LocationAdd = "LocationAdd";
        public const string LocationRemove = "LocationRemove";
        public const string IndicatorAdd = "IndicatorAdd";
        public const string IndicatorRemove = "IndicatorRemove";
    }

    /// <summary>
    /// معاملات تتبع التغييرات
    /// </summary>
    public class ChangeTrackingOptions
    {
        public bool EnableChangeTracking { get; set; } = true;
        public bool EnableVersioning { get; set; } = true;
        public int MaxHistoryDays { get; set; } = 365; // الاحتفاظ بالتاريخ لسنة واحدة
        public int MaxVersions { get; set; } = 50; // الحد الأقصى للإصدارات
        public bool AutoCreateVersions { get; set; } = true;
        public TimeSpan AutoVersionInterval { get; set; } = TimeSpan.FromDays(7); // إصدار جديد كل أسبوع
    }

    /// <summary>
    /// نتيجة عملية الاستعادة
    /// </summary>
    public class RestoreResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = "";
        public string ErrorMessage { get; set; } = "";
        public int RestoredChanges { get; set; }
        public DateTime RestoreDate { get; set; }
    }

    /// <summary>
    /// فلتر تاريخ التغييرات
    /// </summary>
    public class ChangeHistoryFilter
    {
        public int? ProjectId { get; set; }
        public int? LocationNumber { get; set; }
        public string IndicatorNo { get; set; } = "";
        public string MonthColumn { get; set; } = "";
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string ChangeType { get; set; } = "";
        public string ChangedBy { get; set; } = "";
        public int PageSize { get; set; } = 50;
        public int PageNumber { get; set; } = 1;
    }

    /// <summary>
    /// نتيجة البحث في تاريخ التغييرات
    /// </summary>
    public class ChangeHistoryResult
    {
        public List<ChangeHistoryItem> Items { get; set; } = new List<ChangeHistoryItem>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasNextPage => PageNumber < TotalPages;
        public bool HasPreviousPage => PageNumber > 1;
    }
}
