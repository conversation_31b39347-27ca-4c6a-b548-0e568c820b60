using Microsoft.EntityFrameworkCore;
using System.Collections.ObjectModel;
using TYFManagementSystem.Data;
using TYFManagementSystem.Models;
using TYFManagementSystem.Models.Database;
using TYFManagementSystem.Models.IPTT;

namespace TYFManagementSystem.Services
{
    /// <summary>
    /// خدمة إدارة بيانات IPTT في قاعدة البيانات
    /// </summary>
    public class IpttDatabaseService
    {
        private readonly TyfDbContext _context;

        public IpttDatabaseService()
        {
            _context = new TyfDbContext();
            InitializeDatabaseAsync().Wait();
        }

        private async Task InitializeDatabaseAsync()
        {
            try
            {
                await _context.EnsureDatabaseCreatedAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تهيئة قاعدة البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حفظ بيانات IPTT الكاملة للمشروع
        /// </summary>
        public async Task<bool> SaveIpttDataAsync(Project project, ObservableCollection<IpttIndicator> indicators, List<string> monthColumns, Dictionary<int, ObservableCollection<IpttDisplayRow>> locationData)
        {
            try
            {
                using var transaction = await _context.Database.BeginTransactionAsync();

                // حذف البيانات الموجودة للمشروع
                await DeleteExistingProjectDataAsync(project.Id.ToString());

                // حفظ معلومات المشروع
                var projectDb = await SaveProjectInfoAsync(project, monthColumns);

                // حفظ المؤشرات وأنواع البيانات
                await SaveIndicatorsAsync(projectDb.Id, indicators);

                // حفظ بيانات المواقع المتعددة
                await SaveLocationDataAsync(projectDb.Id, locationData, monthColumns);

                await transaction.CommitAsync();
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في حفظ بيانات IPTT: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحميل بيانات IPTT للمشروع
        /// </summary>
        public async Task<IpttLoadResult?> LoadIpttDataAsync(string projectId)
        {
            try
            {
                var projectDb = await _context.IpttProjects
                    .Include(p => p.Indicators)
                        .ThenInclude(i => i.DataTypes)
                    .Include(p => p.Indicators)
                        .ThenInclude(i => i.MonthlyData)
                    .Include(p => p.MonthColumns)
                    .FirstOrDefaultAsync(p => p.ProjectId == projectId);

                if (projectDb == null)
                    return null;

                var result = new IpttLoadResult
                {
                    ProjectInfo = ConvertToProject(projectDb),
                    Indicators = new ObservableCollection<IpttIndicator>(),
                    MonthColumns = projectDb.MonthColumns
                        .OrderBy(m => m.DisplayOrder)
                        .Select(m => m.MonthColumn)
                        .ToList(),
                    LocationData = new Dictionary<int, ObservableCollection<IpttDisplayRow>>()
                };

                // تحويل المؤشرات
                foreach (var indicatorDb in projectDb.Indicators.OrderBy(i => i.IndicatorNumber))
                {
                    var indicator = ConvertToIpttIndicator(indicatorDb);
                    result.Indicators.Add(indicator);
                }

                // تحميل بيانات المواقع
                await LoadLocationDataAsync(projectDb.Id, result);

                return result;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تحميل بيانات IPTT: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// التحقق من وجود بيانات IPTT للمشروع
        /// </summary>
        public async Task<bool> HasIpttDataAsync(string projectId)
        {
            try
            {
                return await _context.IpttProjects.AnyAsync(p => p.ProjectId == projectId);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// حذف جميع بيانات IPTT للمشروع
        /// </summary>
        public async Task<bool> DeleteIpttDataAsync(string projectId)
        {
            try
            {
                await DeleteExistingProjectDataAsync(projectId);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في حذف بيانات IPTT: {ex.Message}", ex);
            }
        }

        private async Task DeleteExistingProjectDataAsync(string projectId)
        {
            var existingProject = await _context.IpttProjects
                .FirstOrDefaultAsync(p => p.ProjectId == projectId);

            if (existingProject != null)
            {
                // حذف بيانات المواقع
                var locationData = _context.IpttLocationData.Where(l => l.ProjectId == existingProject.Id);
                _context.IpttLocationData.RemoveRange(locationData);

                // حذف البيانات الشهرية
                var monthlyData = _context.IpttMonthlyData.Where(m => m.Indicator!.ProjectId == existingProject.Id);
                _context.IpttMonthlyData.RemoveRange(monthlyData);

                // حذف أنواع البيانات
                var dataTypes = _context.IpttDataTypes.Where(d => d.Indicator.ProjectId == existingProject.Id);
                _context.IpttDataTypes.RemoveRange(dataTypes);

                // حذف المؤشرات
                var indicators = _context.IpttIndicators.Where(i => i.ProjectId == existingProject.Id);
                _context.IpttIndicators.RemoveRange(indicators);

                // حذف الأعمدة الشهرية
                var monthColumns = _context.IpttMonthColumns.Where(m => m.ProjectId == existingProject.Id);
                _context.IpttMonthColumns.RemoveRange(monthColumns);

                // حذف المشروع
                _context.IpttProjects.Remove(existingProject);

                await _context.SaveChangesAsync();
            }
        }

        private async Task<IpttProjectDb> SaveProjectInfoAsync(Project project, List<string> monthColumns)
        {
            var projectDb = new IpttProjectDb
            {
                ProjectId = project.Id.ToString(),
                ProjectName = project.Name,
                ProjectManager = project.Manager,
                Status = project.Status,
                StartDate = project.StartDate,
                EndDate = project.EndDate,
                Budget = project.Budget,
                Description = project.Description,
                LastModified = DateTime.Now
            };

            _context.IpttProjects.Add(projectDb);
            await _context.SaveChangesAsync();

            // حفظ الأعمدة الشهرية
            for (int i = 0; i < monthColumns.Count; i++)
            {
                var monthColumn = new IpttMonthColumnDb
                {
                    ProjectId = projectDb.Id,
                    MonthColumn = monthColumns[i],
                    DisplayOrder = i
                };
                _context.IpttMonthColumns.Add(monthColumn);
            }

            await _context.SaveChangesAsync();
            return projectDb;
        }

        private async Task SaveIndicatorsAsync(int projectDbId, ObservableCollection<IpttIndicator> indicators)
        {
            foreach (var indicator in indicators)
            {
                var indicatorDb = new IpttIndicatorDb
                {
                    ProjectId = projectDbId,
                    IndicatorNumber = indicator.No,
                    IndicatorName = indicator.Indicator,
                    LastModified = DateTime.Now
                };

                _context.IpttIndicators.Add(indicatorDb);
                await _context.SaveChangesAsync();

                // حفظ أنواع البيانات
                foreach (var dataType in indicator.DataTypes)
                {
                    var dataTypeDb = new IpttDataTypeDb
                    {
                        IndicatorId = indicatorDb.Id,
                        DataTypeName = dataType.Name,
                        Target = dataType.Target,
                        LastModified = DateTime.Now
                    };

                    _context.IpttDataTypes.Add(dataTypeDb);
                    await _context.SaveChangesAsync();

                    // حفظ البيانات الشهرية لنوع البيانات
                    foreach (var monthlyValue in dataType.MonthlyValues)
                    {
                        if (!string.IsNullOrWhiteSpace(monthlyValue.Value))
                        {
                            var monthlyDataDb = new IpttMonthlyDataDb
                            {
                                DataTypeId = dataTypeDb.Id,
                                MonthColumn = monthlyValue.Key,
                                Value = monthlyValue.Value,
                                LastModified = DateTime.Now
                            };

                            _context.IpttMonthlyData.Add(monthlyDataDb);
                        }
                    }
                }

                await _context.SaveChangesAsync();
            }
        }

        private async Task SaveLocationDataAsync(int projectDbId, Dictionary<int, ObservableCollection<IpttDisplayRow>> locationData, List<string> monthColumns)
        {
            foreach (var location in locationData)
            {
                int locationNumber = location.Key;
                var rows = location.Value;

                // تجاهل الموقع 0 (الإجمالي) لأنه محسوب تلقائياً
                if (locationNumber == 0) continue;

                foreach (var row in rows.Where(r => r.IsDataTypeRow))
                {
                    // العثور على المؤشر والنوع في قاعدة البيانات
                    var indicatorDb = await _context.IpttIndicators
                        .FirstOrDefaultAsync(i => i.ProjectId == projectDbId && i.IndicatorNumber == row.IndicatorId);

                    if (indicatorDb != null)
                    {
                        var dataTypeDb = await _context.IpttDataTypes
                            .FirstOrDefaultAsync(d => d.IndicatorId == indicatorDb.Id && d.DataTypeName == row.DataType);

                        if (dataTypeDb != null)
                        {
                            // حفظ البيانات الشهرية للموقع (بما في ذلك القيم الفارغة)
                            foreach (var monthData in row.MonthlyData)
                            {
                                // حفظ جميع البيانات حتى لو كانت فارغة أو صفر
                                var locationDataDb = new IpttLocationDataDb
                                {
                                    ProjectId = projectDbId,
                                    LocationNumber = locationNumber,
                                    IndicatorId = indicatorDb.Id,
                                    DataTypeId = dataTypeDb.Id,
                                    MonthColumn = monthData.Key,
                                    Value = monthData.Value ?? "", // حفظ القيم الفارغة كنص فارغ
                                    LastModified = DateTime.Now
                                };

                                _context.IpttLocationData.Add(locationDataDb);
                            }
                        }
                    }
                }
            }

            await _context.SaveChangesAsync();
        }

        private async Task LoadLocationDataAsync(int projectDbId, IpttLoadResult result)
        {
            var locationDataList = await _context.IpttLocationData
                .Include(l => l.Indicator)
                .Include(l => l.DataType)
                .Where(l => l.ProjectId == projectDbId)
                .ToListAsync();

            var locationGroups = locationDataList.GroupBy(l => l.LocationNumber);

            // تحديد عدد المواقع من البيانات المحملة
            if (locationGroups.Any())
            {
                result.LocationCount = locationGroups.Max(g => g.Key);
            }

            System.Diagnostics.Debug.WriteLine($"تحميل بيانات المواقع: {locationGroups.Count()} موقع");

            foreach (var locationGroup in locationGroups)
            {
                int locationNumber = locationGroup.Key;
                var displayRows = new ObservableCollection<IpttDisplayRow>();

                System.Diagnostics.Debug.WriteLine($"معالجة الموقع {locationNumber} مع {locationGroup.Count()} سجل");

                // إنشاء صفوف العرض للموقع
                foreach (var indicator in result.Indicators)
                {
                    // إضافة صف المؤشر الرئيسي
                    var mainRow = new IpttDisplayRow
                    {
                        No = indicator.No,
                        Indicator = indicator.Indicator,
                        DataType = "",
                        IsMainIndicator = true,
                        IndicatorId = indicator.No,
                        MonthlyData = new Dictionary<string, string>()
                    };

                    foreach (var month in result.MonthColumns)
                    {
                        mainRow.MonthlyData[month] = "";
                    }

                    displayRows.Add(mainRow);

                    // إضافة صفوف أنواع البيانات
                    foreach (var dataType in indicator.DataTypes)
                    {
                        var dataRow = new IpttDisplayRow
                        {
                            No = "",
                            Indicator = "",
                            DataType = dataType.Name,
                            Target = dataType.Target,
                            IsDataTypeRow = true,
                            IndicatorId = indicator.No,
                            MonthlyData = new Dictionary<string, string>()
                        };

                        // تحميل البيانات الشهرية للموقع
                        foreach (var month in result.MonthColumns)
                        {
                            var locationData = locationGroup.FirstOrDefault(l =>
                                l.Indicator?.IndicatorNumber == indicator.No &&
                                l.DataType?.DataTypeName == dataType.Name &&
                                l.MonthColumn == month);

                            var value = locationData?.Value ?? "";
                            dataRow.MonthlyData[month] = value;

                            if (!string.IsNullOrEmpty(value))
                            {
                                System.Diagnostics.Debug.WriteLine($"تحميل بيانات: موقع {locationNumber}, مؤشر {indicator.No}, {dataType.Name}, {month} = {value}");
                            }
                        }

                        displayRows.Add(dataRow);
                    }
                }

                result.LocationData[locationNumber] = displayRows;
                System.Diagnostics.Debug.WriteLine($"تم إنشاء {displayRows.Count} صف للموقع {locationNumber}");
            }

            System.Diagnostics.Debug.WriteLine($"تم تحميل بيانات {result.LocationData.Count} موقع بنجاح");
        }

        private Project ConvertToProject(IpttProjectDb projectDb)
        {
            return new Project
            {
                Id = int.Parse(projectDb.ProjectId),
                Name = projectDb.ProjectName,
                Manager = projectDb.ProjectManager,
                Status = projectDb.Status,
                StartDate = projectDb.StartDate,
                EndDate = projectDb.EndDate,
                Budget = projectDb.Budget,
                Description = projectDb.Description
            };
        }

        private IpttIndicator ConvertToIpttIndicator(IpttIndicatorDb indicatorDb)
        {
            var indicator = new IpttIndicator
            {
                No = indicatorDb.IndicatorNumber,
                Indicator = indicatorDb.IndicatorName,
                DataTypes = new ObservableCollection<DataTypeItem>()
            };

            foreach (var dataTypeDb in indicatorDb.DataTypes)
            {
                var dataType = new DataTypeItem
                {
                    Name = dataTypeDb.DataTypeName,
                    Target = dataTypeDb.Target,
                    MonthlyValues = new Dictionary<string, string>()
                };

                // تحميل البيانات الشهرية
                foreach (var monthlyDataDb in dataTypeDb.MonthlyData)
                {
                    dataType.MonthlyValues[monthlyDataDb.MonthColumn] = monthlyDataDb.Value;
                }

                indicator.DataTypes.Add(dataType);
            }

            return indicator;
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }

    /// <summary>
    /// نتيجة تحميل بيانات IPTT من قاعدة البيانات
    /// </summary>
    public class IpttLoadResult
    {
        public Project ProjectInfo { get; set; } = null!;
        public ObservableCollection<IpttIndicator> Indicators { get; set; } = new();
        public List<string> MonthColumns { get; set; } = new();
        public Dictionary<int, ObservableCollection<IpttDisplayRow>> LocationData { get; set; } = new();
        public int LocationCount { get; set; } = 1;
    }
}
