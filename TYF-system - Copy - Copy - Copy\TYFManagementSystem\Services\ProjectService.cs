using System;
using System.Collections.ObjectModel;
using System.Linq;
using TYFManagementSystem.Models;

namespace TYFManagementSystem.Services
{
    public class ProjectService
    {
        private static ProjectService? _instance;
        private ObservableCollection<Project> _projects;
        private readonly ProjectDatabaseService _databaseService;

        public static ProjectService Instance => _instance ??= new ProjectService();

        private ProjectService()
        {
            _projects = new ObservableCollection<Project>();
            _databaseService = new ProjectDatabaseService();
            InitializeFromDatabase();
        }

        public ObservableCollection<Project> GetAllProjects()
        {
            return _projects;
        }

        public async void AddProject(Project project)
        {
            try
            {
                if (string.IsNullOrEmpty(project.ProjectNumber))
                {
                    project.ProjectNumber = await _databaseService.GenerateProjectNumberAsync();
                }

                await _databaseService.AddProjectAsync(project);
                _projects.Add(project);
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في إضافة المشروع: {ex.Message}", ex);
            }
        }

        private string GenerateProjectNumber()
        {
            var year = DateTime.Now.Year;
            var nextNumber = _projects.Count + 1;
            return $"{year}-TYF-{nextNumber:D3}";
        }

        public async void UpdateProject(Project project)
        {
            try
            {
                await _databaseService.UpdateProjectAsync(project);

                var existingProject = _projects.FirstOrDefault(p => p.Id == project.Id);
                if (existingProject != null)
                {
                    existingProject.ProjectNumber = project.ProjectNumber;
                    existingProject.ProjectCode = project.ProjectCode;
                    existingProject.Name = project.Name;
                    existingProject.Region = project.Region;
                    existingProject.Description = project.Description;
                    existingProject.StartDate = project.StartDate;
                    existingProject.EndDate = project.EndDate;
                    existingProject.Status = project.Status;
                    existingProject.Budget = project.Budget;
                    existingProject.Manager = project.Manager;
                    existingProject.Beneficiaries = project.Beneficiaries;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تحديث المشروع: {ex.Message}", ex);
            }
        }

        public async void DeleteProject(int projectId)
        {
            try
            {
                await _databaseService.DeleteProjectAsync(projectId);

                var project = _projects.FirstOrDefault(p => p.Id == projectId);
                if (project != null)
                {
                    _projects.Remove(project);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في حذف المشروع: {ex.Message}", ex);
            }
        }

        public Project? GetProjectById(int id)
        {
            return _projects.FirstOrDefault(p => p.Id == id);
        }

        private async void InitializeFromDatabase()
        {
            try
            {
                // التأكد من تهيئة قاعدة البيانات أولاً
                await DatabaseInitializer.InitializeDatabaseAsync();

                var projectsFromDb = await _databaseService.GetAllProjectsAsync();
                _projects.Clear();

                foreach (var project in projectsFromDb)
                {
                    _projects.Add(project);
                }

                // إذا لم توجد مشاريع في قاعدة البيانات، أضف البيانات النموذجية
                if (_projects.Count == 0)
                {
                    await InitializeSampleDataAsync();
                }
            }
            catch (Exception ex)
            {
                // في حالة فشل تحميل البيانات من قاعدة البيانات، استخدم البيانات النموذجية
                System.Windows.MessageBox.Show($"تحذير: فشل في تحميل البيانات من قاعدة البيانات. سيتم استخدام البيانات النموذجية.\nالخطأ: {ex.Message}",
                    "تحذير", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                InitializeSampleDataInMemory();
            }
        }

        private async Task InitializeSampleDataAsync()
        {
            var sampleProjects = GetSampleProjects();

            foreach (var project in sampleProjects)
            {
                try
                {
                    await _databaseService.AddProjectAsync(project);
                    _projects.Add(project);
                }
                catch (Exception)
                {
                    // تجاهل الأخطاء في البيانات النموذجية
                }
            }
        }

        private void InitializeSampleDataInMemory()
        {
            var sampleProjects = GetSampleProjects();

            foreach (var project in sampleProjects)
            {
                _projects.Add(project);
            }
        }

        private List<Project> GetSampleProjects()
        {
            return new List<Project>
            {
                new Project
                {
                    Id = 1,
                    ProjectNumber = "2024-TYF-001",
                    ProjectCode = "ORF-001",
                    Name = "مشروع كفالة الأيتام",
                    Region = "الرياض",
                    Description = "برنامج كفالة شاملة للأطفال الأيتام",
                    StartDate = new DateTime(2024, 1, 1),
                    EndDate = new DateTime(2024, 12, 31),
                    Status = "نشط",
                    Budget = 50000,
                    Manager = "أحمد محمد",
                    Beneficiaries = 150
                },
                new Project
                {
                    Id = 2,
                    ProjectNumber = "2024-TYF-002",
                    ProjectCode = "EDU-001",
                    Name = "مشروع التعليم المجتمعي",
                    Region = "جدة",
                    Description = "برنامج محو الأمية وتعليم الكبار",
                    StartDate = new DateTime(2024, 2, 1),
                    EndDate = new DateTime(2024, 11, 30),
                    Status = "نشط",
                    Budget = 30000,
                    Manager = "فاطمة علي",
                    Beneficiaries = 200
                },
                new Project
                {
                    Id = 3,
                    ProjectNumber = "2024-TYF-003",
                    ProjectCode = "REL-001",
                    Name = "مشروع الإغاثة الطارئة",
                    Region = "الدمام",
                    Description = "توزيع المساعدات الغذائية والطبية",
                    StartDate = new DateTime(2024, 3, 1),
                    EndDate = new DateTime(2024, 6, 30),
                    Status = "مكتمل",
                    Budget = 75000,
                    Manager = "محمد حسن",
                    Beneficiaries = 500
                },
                new Project
                {
                    Id = 4,
                    ProjectNumber = "2024-TYF-004",
                    ProjectCode = "TRN-001",
                    Name = "مشروع التدريب المهني",
                    Region = "مكة المكرمة",
                    Description = "تدريب الشباب على المهن والحرف",
                    StartDate = new DateTime(2024, 4, 1),
                    EndDate = new DateTime(2024, 10, 31),
                    Status = "نشط",
                    Budget = 40000,
                    Manager = "سارة أحمد",
                    Beneficiaries = 100
                },
                new Project
                {
                    Id = 5,
                    ProjectNumber = "2024-TYF-005",
                    ProjectCode = "HLT-001",
                    Name = "مشروع الرعاية الصحية",
                    Region = "المدينة المنورة",
                    Description = "توفير الخدمات الصحية للمحتاجين",
                    StartDate = new DateTime(2024, 5, 1),
                    EndDate = new DateTime(2024, 12, 31),
                    Status = "نشط",
                    Budget = 60000,
                    Manager = "خالد الأحمد",
                    Beneficiaries = 300
                },
                new Project
                {
                    Id = 6,
                    ProjectNumber = "2024-TYF-006",
                    ProjectCode = "HSG-001",
                    Name = "مشروع الإسكان الخيري",
                    Region = "الطائف",
                    Description = "توفير السكن المناسب للأسر المحتاجة",
                    StartDate = new DateTime(2024, 6, 1),
                    EndDate = new DateTime(2025, 6, 30),
                    Status = "مخطط",
                    Budget = 120000,
                    Manager = "نورا السالم",
                    Beneficiaries = 80
                }
            };
        }

        /// <summary>
        /// إعادة تحميل المشاريع من قاعدة البيانات
        /// </summary>
        public async Task RefreshFromDatabaseAsync()
        {
            try
            {
                var projectsFromDb = await _databaseService.GetAllProjectsAsync();
                _projects.Clear();

                foreach (var project in projectsFromDb)
                {
                    _projects.Add(project);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في إعادة تحميل المشاريع: {ex.Message}", ex);
            }
        }
    }
}
