using System.Collections.ObjectModel;
using System.Windows.Input;
using TYFManagementSystem.Commands;
using TYFManagementSystem.Models.IPTT;
using TYFManagementSystem.Services.IPTT;
using TYFManagementSystem.ViewModels;

namespace TYFManagementSystem.ViewModels.IPTT
{
    /// <summary>
    /// ViewModel لإدارة المؤشرات في IPTT
    /// </summary>
    public class IpttIndicatorViewModel : BaseViewModel
    {
        #region Private Fields
        private readonly IpttValidationService _validationService;
        
        private IpttIndicator _indicator;
        private bool _isEditing;
        private bool _hasChanges;
        private string _validationMessage = "";
        #endregion

        #region Public Properties
        public IpttIndicator Indicator
        {
            get => _indicator;
            set => SetProperty(ref _indicator, value);
        }

        public string No
        {
            get => Indicator?.No ?? "";
            set
            {
                if (Indicator != null && Indicator.No != value)
                {
                    Indicator.No = value;
                    OnPropertyChanged();
                    OnIndicatorChanged();
                }
            }
        }

        public string IndicatorName
        {
            get => Indicator?.Indicator ?? "";
            set
            {
                if (Indicator != null && Indicator.Indicator != value)
                {
                    Indicator.Indicator = value;
                    OnPropertyChanged();
                    OnIndicatorChanged();
                }
            }
        }

        public ObservableCollection<DataTypeItem> DataTypes
        {
            get => Indicator?.DataTypes ?? new ObservableCollection<DataTypeItem>();
            set
            {
                if (Indicator != null)
                {
                    Indicator.DataTypes = value;
                    OnPropertyChanged();
                    OnIndicatorChanged();
                }
            }
        }

        public bool IsEditing
        {
            get => _isEditing;
            set => SetProperty(ref _isEditing, value);
        }

        public bool HasChanges
        {
            get => _hasChanges;
            set => SetProperty(ref _hasChanges, value);
        }

        public string ValidationMessage
        {
            get => _validationMessage;
            set => SetProperty(ref _validationMessage, value);
        }

        public bool IsValid => string.IsNullOrEmpty(ValidationMessage);
        #endregion

        #region Commands
        public ICommand AddDataTypeCommand { get; private set; }
        public ICommand RemoveDataTypeCommand { get; private set; }
        public ICommand ValidateCommand { get; private set; }
        public ICommand StartEditCommand { get; private set; }
        public ICommand SaveChangesCommand { get; private set; }
        public ICommand CancelChangesCommand { get; private set; }
        #endregion

        #region Events
        public event EventHandler<IndicatorChangedEventArgs>? IndicatorChanged;
        public event EventHandler<IndicatorValidatedEventArgs>? IndicatorValidated;
        #endregion

        #region Constructor
        public IpttIndicatorViewModel(IpttIndicator indicator = null)
        {
            _validationService = new IpttValidationService();
            
            Indicator = indicator ?? new IpttIndicator
            {
                No = "",
                Indicator = "",
                DataTypes = new ObservableCollection<DataTypeItem>()
            };

            InitializeCommands();
            SetupDataTypeChangeTracking();
            Validate();
        }
        #endregion

        #region Command Initialization
        private void InitializeCommands()
        {
            AddDataTypeCommand = new RelayCommand(AddDataType);
            RemoveDataTypeCommand = new RelayCommand(param => RemoveDataType(param as DataTypeItem), param => param != null);
            ValidateCommand = new RelayCommand(Validate);
            StartEditCommand = new RelayCommand(StartEdit, () => !IsEditing);
            SaveChangesCommand = new RelayCommand(SaveChanges, () => IsEditing && IsValid);
            CancelChangesCommand = new RelayCommand(CancelChanges, () => IsEditing);
        }
        #endregion

        #region Data Type Management
        private void AddDataType()
        {
            try
            {
                var newDataType = new DataTypeItem
                {
                    Name = $"نوع البيانات {DataTypes.Count + 1}",
                    Target = "0",
                    MonthlyValues = new Dictionary<string, string>()
                };

                DataTypes.Add(newDataType);
                OnIndicatorChanged();
                
                ValidationMessage = "";
            }
            catch (Exception ex)
            {
                ValidationMessage = $"خطأ في إضافة نوع البيانات: {ex.Message}";
            }
        }

        private void RemoveDataType(DataTypeItem dataType)
        {
            try
            {
                if (dataType != null && DataTypes.Contains(dataType))
                {
                    DataTypes.Remove(dataType);
                    OnIndicatorChanged();
                    Validate();
                }
            }
            catch (Exception ex)
            {
                ValidationMessage = $"خطأ في حذف نوع البيانات: {ex.Message}";
            }
        }

        private void SetupDataTypeChangeTracking()
        {
            DataTypes.CollectionChanged += (s, e) =>
            {
                OnIndicatorChanged();
                Validate();
            };
        }
        #endregion

        #region Validation
        private void Validate()
        {
            try
            {
                var validation = _validationService.ValidateIndicator(Indicator);
                
                if (validation.IsValid)
                {
                    ValidationMessage = "";
                }
                else
                {
                    ValidationMessage = string.Join("; ", validation.Errors);
                }

                OnPropertyChanged(nameof(IsValid));
                OnIndicatorValidated(validation);
            }
            catch (Exception ex)
            {
                ValidationMessage = $"خطأ في التحقق: {ex.Message}";
                OnPropertyChanged(nameof(IsValid));
            }
        }
        #endregion

        #region Editing Operations
        private IpttIndicator _originalIndicator;

        private void StartEdit()
        {
            try
            {
                // حفظ نسخة من البيانات الأصلية
                _originalIndicator = CloneIndicator(Indicator);
                IsEditing = true;
                ValidationMessage = "";
            }
            catch (Exception ex)
            {
                ValidationMessage = $"خطأ في بدء التحرير: {ex.Message}";
            }
        }

        private void SaveChanges()
        {
            try
            {
                if (!IsValid)
                {
                    ValidationMessage = "لا يمكن حفظ البيانات - توجد أخطاء في التحقق";
                    return;
                }

                IsEditing = false;
                HasChanges = false;
                _originalIndicator = null;
                
                OnIndicatorChanged();
                ValidationMessage = "تم حفظ التغييرات";
            }
            catch (Exception ex)
            {
                ValidationMessage = $"خطأ في حفظ التغييرات: {ex.Message}";
            }
        }

        private void CancelChanges()
        {
            try
            {
                if (_originalIndicator != null)
                {
                    // استعادة البيانات الأصلية
                    Indicator = CloneIndicator(_originalIndicator);
                    OnPropertyChanged(nameof(No));
                    OnPropertyChanged(nameof(IndicatorName));
                    OnPropertyChanged(nameof(DataTypes));
                }

                IsEditing = false;
                HasChanges = false;
                _originalIndicator = null;
                
                Validate();
                ValidationMessage = "تم إلغاء التغييرات";
            }
            catch (Exception ex)
            {
                ValidationMessage = $"خطأ في إلغاء التغييرات: {ex.Message}";
            }
        }

        private IpttIndicator CloneIndicator(IpttIndicator source)
        {
            return new IpttIndicator
            {
                No = source.No,
                Indicator = source.Indicator,
                DataType = source.DataType,
                Target = source.Target,
                MonthlyValues = new Dictionary<string, string>(source.MonthlyValues),
                DataTypes = new ObservableCollection<DataTypeItem>(
                    source.DataTypes.Select(dt => new DataTypeItem
                    {
                        Name = dt.Name,
                        Target = dt.Target,
                        MonthlyValues = new Dictionary<string, string>(dt.MonthlyValues)
                    })
                )
            };
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// تحديث نوع بيانات معين
        /// </summary>
        public void UpdateDataType(string dataTypeName, string newName, string newTarget)
        {
            try
            {
                var dataType = DataTypes.FirstOrDefault(dt => dt.Name == dataTypeName);
                if (dataType != null)
                {
                    dataType.Name = newName;
                    dataType.Target = newTarget;
                    OnIndicatorChanged();
                    Validate();
                }
            }
            catch (Exception ex)
            {
                ValidationMessage = $"خطأ في تحديث نوع البيانات: {ex.Message}";
            }
        }

        /// <summary>
        /// تحديث البيانات الشهرية لنوع بيانات معين
        /// </summary>
        public void UpdateMonthlyData(string dataTypeName, string month, string value)
        {
            try
            {
                var dataType = DataTypes.FirstOrDefault(dt => dt.Name == dataTypeName);
                if (dataType != null)
                {
                    dataType.MonthlyValues[month] = value;
                    OnIndicatorChanged();
                }
            }
            catch (Exception ex)
            {
                ValidationMessage = $"خطأ في تحديث البيانات الشهرية: {ex.Message}";
            }
        }

        /// <summary>
        /// الحصول على ملخص المؤشر
        /// </summary>
        public IndicatorSummary GetSummary()
        {
            try
            {
                double totalTarget = 0;
                double totalAchievement = 0;
                int dataTypeCount = DataTypes.Count;

                foreach (var dataType in DataTypes)
                {
                    if (double.TryParse(dataType.Target, out double target))
                        totalTarget += target;

                    // حساب الإنجاز من البيانات الشهرية
                    double achievement = 0;
                    foreach (var monthValue in dataType.MonthlyValues.Values)
                    {
                        if (double.TryParse(monthValue, out double value))
                            achievement += value;
                    }
                    totalAchievement += achievement;
                }

                return new IndicatorSummary
                {
                    IndicatorId = Indicator.No,
                    IndicatorName = Indicator.Indicator,
                    DataTypeCount = dataTypeCount,
                    TotalTarget = totalTarget,
                    TotalAchievement = totalAchievement,
                    CompletionPercentage = totalTarget > 0 ? (totalAchievement / totalTarget) * 100 : 0,
                    IsValid = IsValid
                };
            }
            catch (Exception ex)
            {
                ValidationMessage = $"خطأ في إنشاء الملخص: {ex.Message}";
                return new IndicatorSummary
                {
                    IndicatorId = Indicator?.No ?? "",
                    IndicatorName = Indicator?.Indicator ?? "",
                    IsValid = false
                };
            }
        }

        /// <summary>
        /// إعادة تعيين البيانات الشهرية
        /// </summary>
        public void ResetMonthlyData()
        {
            try
            {
                foreach (var dataType in DataTypes)
                {
                    dataType.MonthlyValues.Clear();
                }
                OnIndicatorChanged();
                ValidationMessage = "تم مسح البيانات الشهرية";
            }
            catch (Exception ex)
            {
                ValidationMessage = $"خطأ في مسح البيانات: {ex.Message}";
            }
        }

        /// <summary>
        /// تهيئة البيانات الشهرية بناءً على أعمدة الأشهر
        /// </summary>
        public void InitializeMonthlyData(List<string> monthColumns)
        {
            try
            {
                foreach (var dataType in DataTypes)
                {
                    foreach (var month in monthColumns)
                    {
                        if (!dataType.MonthlyValues.ContainsKey(month))
                        {
                            dataType.MonthlyValues[month] = "";
                        }
                    }
                }
                OnIndicatorChanged();
            }
            catch (Exception ex)
            {
                ValidationMessage = $"خطأ في تهيئة البيانات الشهرية: {ex.Message}";
            }
        }
        #endregion

        #region Event Handlers
        private void OnIndicatorChanged()
        {
            HasChanges = true;
            IndicatorChanged?.Invoke(this, new IndicatorChangedEventArgs(Indicator));
        }

        private void OnIndicatorValidated(IpttValidationService.ValidationResult validation)
        {
            IndicatorValidated?.Invoke(this, new IndicatorValidatedEventArgs(Indicator, validation));
        }
        #endregion
    }

    #region Event Args Classes
    public class IndicatorChangedEventArgs : EventArgs
    {
        public IpttIndicator Indicator { get; }

        public IndicatorChangedEventArgs(IpttIndicator indicator)
        {
            Indicator = indicator;
        }
    }

    public class IndicatorValidatedEventArgs : EventArgs
    {
        public IpttIndicator Indicator { get; }
        public IpttValidationService.ValidationResult ValidationResult { get; }

        public IndicatorValidatedEventArgs(IpttIndicator indicator, IpttValidationService.ValidationResult validationResult)
        {
            Indicator = indicator;
            ValidationResult = validationResult;
        }
    }

    public class IndicatorSummary
    {
        public string IndicatorId { get; set; } = "";
        public string IndicatorName { get; set; } = "";
        public int DataTypeCount { get; set; }
        public double TotalTarget { get; set; }
        public double TotalAchievement { get; set; }
        public double CompletionPercentage { get; set; }
        public bool IsValid { get; set; }
    }
    #endregion
}
