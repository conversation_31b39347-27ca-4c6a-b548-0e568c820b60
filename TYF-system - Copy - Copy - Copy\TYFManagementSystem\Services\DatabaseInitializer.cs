using TYFManagementSystem.Data;
using Microsoft.EntityFrameworkCore;
using System.IO;

namespace TYFManagementSystem.Services
{
    /// <summary>
    /// خدمة تهيئة قاعدة البيانات
    /// </summary>
    public static class DatabaseInitializer
    {
        /// <summary>
        /// تهيئة قاعدة البيانات وإنشاء الجداول المطلوبة
        /// </summary>
        public static async Task InitializeDatabaseAsync()
        {
            try
            {
                // تطبيق ترحيل قاعدة البيانات وإضافة الجداول الجديدة
                await DatabaseMigrationService.MigrateDatabaseAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تهيئة قاعدة البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// التحقق من صحة اتصال قاعدة البيانات
        /// </summary>
        public static async Task<bool> TestDatabaseConnectionAsync()
        {
            try
            {
                using var context = new TyfDbContext();
                return await context.Database.CanConnectAsync();
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية من قاعدة البيانات
        /// </summary>
        public static async Task<bool> BackupDatabaseAsync(string backupPath)
        {
            try
            {
                using var context = new TyfDbContext();
                
                // للـ SQLite، يمكن نسخ ملف قاعدة البيانات مباشرة
                var dbPath = context.Database.GetConnectionString();
                if (dbPath != null && dbPath.Contains("Data Source="))
                {
                    var sourcePath = dbPath.Replace("Data Source=", "");
                    if (File.Exists(sourcePath))
                    {
                        File.Copy(sourcePath, backupPath, true);
                        return true;
                    }
                }
                
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}
