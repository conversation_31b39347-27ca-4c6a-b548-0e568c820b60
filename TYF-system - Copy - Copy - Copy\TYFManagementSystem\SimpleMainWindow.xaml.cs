using System.Windows;
using System.Windows.Controls;
using TYFManagementSystem.Views.Projects;

namespace TYFManagementSystem;

/// <summary>
/// Interaction logic for SimpleMainWindow.xaml
/// </summary>
public partial class SimpleMainWindow : Window
{
    public SimpleMainWindow()
    {
        InitializeComponent();
    }

    private void ProjectsButton_Click(object sender, RoutedEventArgs e)
    {
        // Replace main content with Projects view
        var projectsView = new ProjectsView();
        MainContentArea.Content = projectsView;
    }

    private void DashboardButton_Click(object sender, RoutedEventArgs e)
    {
        // Return to dashboard
        MainContentArea.Content = null;
    }
}
