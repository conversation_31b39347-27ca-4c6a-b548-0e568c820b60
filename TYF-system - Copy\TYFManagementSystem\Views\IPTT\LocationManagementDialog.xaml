<Window x:Class="TYFManagementSystem.Views.IPTT.LocationManagementDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:TYFManagementSystem.Views.IPTT"
        mc:Ignorable="d"
        Title="إدارة المواقع"
        Height="500"
        Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="#f8f9fa"
        ResizeMode="NoResize"
        Loaded="Window_Loaded">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        <local:InverseBoolToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#FF9800" CornerRadius="0,0,15,15">
            <StackPanel Orientation="Horizontal"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center">
                <TextBlock Text="📍"
                         FontSize="24"
                         Margin="0,0,10,0"/>
                <StackPanel>
                    <TextBlock Text="إدارة مواقع المشروع"
                              FontSize="16"
                              FontWeight="Bold"
                              Foreground="White"/>
                    <TextBlock Text="تعديل عدد المواقع وإعداداتها"
                              FontSize="12"
                              Foreground="White"
                              Margin="0,3,0,0"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Content -->
        <Border Grid.Row="1" Background="White" Margin="20,10" CornerRadius="8" Padding="20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
            </Border.Effect>
            
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="20"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="20"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Current Location Count -->
                <StackPanel Grid.Row="0" Orientation="Horizontal">
                    <TextBlock Text="العدد الحالي للمواقع:"
                             FontSize="14"
                             FontWeight="Bold"
                             VerticalAlignment="Center"
                             Margin="0,0,10,0"/>
                    <TextBlock Text="{Binding CurrentLocationCount}"
                             FontSize="14"
                             Foreground="#2E7D32"
                             FontWeight="Bold"
                             VerticalAlignment="Center"/>
                </StackPanel>

                <!-- New Location Count -->
                <StackPanel Grid.Row="2" Orientation="Horizontal">
                    <TextBlock Text="العدد الجديد للمواقع:"
                             FontSize="14"
                             FontWeight="Bold"
                             VerticalAlignment="Center"
                             Margin="0,0,10,0"/>
                    <TextBox x:Name="NewLocationCountTextBox"
                           Text="{Binding NewLocationCount, UpdateSourceTrigger=PropertyChanged}"
                           Width="80"
                           Height="30"
                           FontSize="14"
                           HorizontalContentAlignment="Center"
                           VerticalContentAlignment="Center"
                           BorderBrush="#ddd"
                           BorderThickness="1"
                           CornerRadius="4"/>
                    
                    <Button Content="معاينة التغييرات"
                            Command="{Binding PreviewChangesCommand}"
                            Background="#2196F3"
                            Foreground="White"
                            Padding="15,8"
                            Margin="15,0,0,0"
                            FontSize="12"
                            FontWeight="Bold"
                            BorderThickness="0"
                            CornerRadius="4"
                            Cursor="Hand"/>
                </StackPanel>

                <!-- Preview Area -->
                <Border Grid.Row="4" 
                        Background="#F5F5F5" 
                        CornerRadius="8" 
                        Padding="15"
                        Visibility="{Binding ShowPreview, Converter={StaticResource BoolToVisibilityConverter}}">
                    
                    <ScrollViewer VerticalScrollBarVisibility="Auto" MaxHeight="200">
                        <StackPanel>
                            <!-- Warning Message -->
                            <Border Background="#FFF3CD" 
                                    BorderBrush="#FFEAA7" 
                                    BorderThickness="1" 
                                    CornerRadius="4" 
                                    Padding="10"
                                    Margin="0,0,0,15"
                                    Visibility="{Binding HasDataLoss, Converter={StaticResource BoolToVisibilityConverter}}">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="⚠️" FontSize="16" Margin="0,0,10,0"/>
                                    <TextBlock Text="تحذير: هذا التغيير سيؤدي إلى فقدان بيانات!"
                                             FontWeight="Bold"
                                             Foreground="#856404"/>
                                </StackPanel>
                            </Border>

                            <!-- Success Message -->
                            <Border Background="#D4EDDA" 
                                    BorderBrush="#C3E6CB" 
                                    BorderThickness="1" 
                                    CornerRadius="4" 
                                    Padding="10"
                                    Margin="0,0,0,15"
                                    Visibility="{Binding HasDataLoss, Converter={StaticResource InverseBoolToVisibilityConverter}}">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="✅" FontSize="16" Margin="0,0,10,0"/>
                                    <TextBlock Text="لا توجد مخاطر فقدان بيانات"
                                             FontWeight="Bold"
                                             Foreground="#155724"/>
                                </StackPanel>
                            </Border>

                            <!-- Changes Summary -->
                            <TextBlock Text="ملخص التغييرات:"
                                     FontSize="14"
                                     FontWeight="Bold"
                                     Margin="0,0,0,10"/>

                            <ItemsControl ItemsSource="{Binding ChangesSummary}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" Margin="0,2">
                                            <TextBlock Text="{Binding Icon}" 
                                                     FontSize="14" 
                                                     Margin="0,0,8,0"/>
                                            <TextBlock Text="{Binding Description}" 
                                                     FontSize="12"
                                                     Foreground="{Binding Color}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>

                            <!-- Data Loss Details -->
                            <StackPanel Visibility="{Binding HasDataLoss, Converter={StaticResource BoolToVisibilityConverter}}"
                                      Margin="0,15,0,0">
                                <TextBlock Text="البيانات التي ستفقد:"
                                         FontSize="14"
                                         FontWeight="Bold"
                                         Foreground="#D32F2F"
                                         Margin="0,0,0,10"/>

                                <ItemsControl ItemsSource="{Binding DataLossDetails}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Background="#FFEBEE" 
                                                    BorderBrush="#FFCDD2" 
                                                    BorderThickness="1" 
                                                    CornerRadius="4" 
                                                    Padding="8" 
                                                    Margin="0,2">
                                                <StackPanel>
                                                    <TextBlock Text="{Binding LocationName}" 
                                                             FontWeight="Bold" 
                                                             FontSize="12"/>
                                                    <TextBlock Text="{Binding DataSummary}" 
                                                             FontSize="11" 
                                                             Foreground="#666"
                                                             Margin="0,2,0,0"/>
                                                </StackPanel>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>

                            <!-- Backup Recommendation -->
                            <Border Background="#E3F2FD" 
                                    BorderBrush="#BBDEFB" 
                                    BorderThickness="1" 
                                    CornerRadius="4" 
                                    Padding="10"
                                    Margin="0,15,0,0"
                                    Visibility="{Binding HasDataLoss, Converter={StaticResource BoolToVisibilityConverter}}">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                        <TextBlock Text="💡" FontSize="14" Margin="0,0,8,0"/>
                                        <TextBlock Text="توصية:"
                                                 FontWeight="Bold"
                                                 FontSize="12"
                                                 Foreground="#1976D2"/>
                                    </StackPanel>
                                    <TextBlock Text="يُنصح بإنشاء نسخة احتياطية قبل المتابعة"
                                             FontSize="11"
                                             Foreground="#1976D2"
                                             TextWrapping="Wrap"/>
                                    <Button Content="إنشاء نسخة احتياطية الآن"
                                            Command="{Binding CreateBackupCommand}"
                                            Background="#1976D2"
                                            Foreground="White"
                                            Padding="10,5"
                                            Margin="0,8,0,0"
                                            FontSize="11"
                                            BorderThickness="0"
                                            CornerRadius="3"
                                            HorizontalAlignment="Left"
                                            Cursor="Hand"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </ScrollViewer>
                </Border>
            </Grid>
        </Border>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="White" Margin="20,0,20,10" CornerRadius="8" Padding="15">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="1" Opacity="0.2"/>
            </Border.Effect>
            
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="تطبيق التغييرات"
                        Command="{Binding ApplyChangesCommand}"
                        Background="#4CAF50"
                        Foreground="White"
                        Padding="20,10"
                        Margin="10,0"
                        FontSize="14"
                        FontWeight="Bold"
                        BorderThickness="0"
                        CornerRadius="5"
                        Cursor="Hand"
                        IsEnabled="{Binding CanApplyChanges}"/>

                <Button Content="إلغاء"
                        Command="{Binding CancelCommand}"
                        Background="#9E9E9E"
                        Foreground="White"
                        Padding="20,10"
                        Margin="10,0"
                        FontSize="14"
                        FontWeight="Bold"
                        BorderThickness="0"
                        CornerRadius="5"
                        Cursor="Hand"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
