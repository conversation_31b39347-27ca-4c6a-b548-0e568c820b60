<Window x:Class="TYFManagementSystem.Views.IPTT.AddIndicatorDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="إضافة مؤشر جديد"
        Height="600"
        Width="700"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        Background="#f8f9fa"
        ResizeMode="CanResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" 
                Background="#9C27B0" 
                CornerRadius="8" 
                Padding="15" 
                Margin="0,0,0,20">
            <TextBlock Text="إضافة مؤشر جديد" 
                      FontSize="18" 
                      FontWeight="Bold" 
                      Foreground="White" 
                      HorizontalAlignment="Center"/>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Indicator Number -->
                <TextBlock Text="رقم المؤشر:" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="IndicatorNumberTextBox" 
                        Height="35" 
                        FontSize="12" 
                        Padding="8"
                        Margin="0,0,0,15"/>

                <!-- Indicator Name -->
                <TextBlock Text="اسم المؤشر:" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="IndicatorNameTextBox" 
                        Height="35" 
                        FontSize="12" 
                        Padding="8"
                        Margin="0,0,0,15"/>

                <!-- Data Types Section -->
                <Border Background="White" 
                        CornerRadius="8" 
                        Padding="15" 
                        Margin="0,0,0,15"
                        BorderBrush="#ddd" 
                        BorderThickness="1">
                    <StackPanel>
                        <TextBlock Text="أنواع البيانات:" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                        
                        <!-- Data Types List -->
                        <StackPanel x:Name="DataTypesPanel">
                            <!-- Initial data type row will be added here -->
                        </StackPanel>
                        
                        <!-- Add Data Type Button -->
                        <Button x:Name="AddDataTypeRowButton"
                                Content="إدخال نوع بيانات آخر"
                                Width="150"
                                Height="30"
                                Background="#FF9800"
                                Foreground="White"
                                FontSize="12"
                                FontWeight="Bold"
                                BorderThickness="0"
                                Margin="0,10,0,0"
                                HorizontalAlignment="Left"
                                Click="AddDataTypeRowButton_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                            CornerRadius="5"
                                            BorderThickness="{TemplateBinding BorderThickness}">
                                        <StackPanel Orientation="Horizontal"
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center">
                                            <TextBlock Text="➕"
                                                     FontSize="14"
                                                     Margin="0,0,5,0"/>
                                            <ContentPresenter/>
                                        </StackPanel>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#F57C00"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- Footer Buttons -->
        <StackPanel Grid.Row="2" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Center" 
                   Margin="0,20,0,0">
            <Button x:Name="SaveButton"
                    Content="حفظ المؤشر"
                    Width="120"
                    Height="40"
                    Background="#4CAF50"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="Bold"
                    BorderThickness="0"
                    Margin="10,0"
                    Click="SaveButton_Click">
                <Button.Template>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <StackPanel Orientation="Horizontal"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center">
                                <TextBlock Text="💾"
                                         FontSize="16"
                                         Margin="0,0,8,0"/>
                                <ContentPresenter/>
                            </StackPanel>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#45a049"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3d8b40"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Button.Template>
            </Button>

            <Button x:Name="CancelButton"
                    Content="إلغاء"
                    Width="120"
                    Height="40"
                    Background="#757575"
                    Foreground="White"
                    FontSize="14"
                    FontWeight="Bold"
                    BorderThickness="0"
                    Margin="10,0"
                    Click="CancelButton_Click">
                <Button.Template>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <StackPanel Orientation="Horizontal"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center">
                                <TextBlock Text="❌"
                                         FontSize="16"
                                         Margin="0,0,8,0"/>
                                <ContentPresenter/>
                            </StackPanel>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#9E9E9E"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#616161"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Button.Template>
            </Button>
        </StackPanel>
    </Grid>
</Window>
