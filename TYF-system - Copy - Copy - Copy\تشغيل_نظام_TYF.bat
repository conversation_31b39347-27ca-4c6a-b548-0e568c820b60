@echo off
title تشغيل نظام TYF Management System
color 0A

echo.
echo ========================================
echo       تشغيل نظام TYF Management System
echo ========================================
echo.

echo 📍 التحقق من وجود المجلد...
if not exist "%~dp0TYFManagementSystem" (
    echo ❌ مجلد TYFManagementSystem غير موجود!
    echo المسار المتوقع: %~dp0TYFManagementSystem
    echo.
    echo المحتويات الحالية:
    dir "%~dp0"
    echo.
    echo تأكد من وجود مجلد TYFManagementSystem في نفس مكان هذا الملف.
    pause
    exit /b 1
)

echo ✅ تم العثور على مجلد المشروع
echo.

echo 🚀 بدء تشغيل النظام...
cd /d "%~dp0TYFManagementSystem"

echo المسار الحالي: %CD%
echo.

call run.bat

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ فشل في تشغيل النظام!
    echo جرب الحلول التالية:
    echo 1. تأكد من تثبيت .NET SDK
    echo 2. شغل الملف كمدير
    echo 3. تحقق من الصلاحيات
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ تم إغلاق النظام بنجاح
pause
