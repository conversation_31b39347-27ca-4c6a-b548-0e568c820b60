<Window x:Class="TYFManagementSystem.Views.DatabaseDiagnosticWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تشخيص وإصلاح قاعدة البيانات" 
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="12">

    <Window.Resources>
        <Style TargetType="Button">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Background" Value="#4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#45a049"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style TargetType="TextBox">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="5"/>
            <Setter Property="BorderBrush" Value="#ddd"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>

        <Style TargetType="TextBlock">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
    </Window.Resources>

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0"
                   Text="🔧 أداة تشخيص وإصلاح مشاكل قاعدة بيانات IPTT"
                   FontSize="16"
                   FontWeight="Bold"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20"
                   Foreground="#2196F3"/>

        <!-- أدوات التحكم -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center">
            <TextBlock Text="معرف المشروع:" VerticalAlignment="Center"/>
            <TextBox x:Name="ProjectIdTextBox"
                     Width="200"
                     Text="1"
                     ToolTip="أدخل معرف المشروع المراد فحصه"/>

            <Button x:Name="DiagnoseButton"
                    Content="🔍 تشخيص"
                    Click="DiagnoseButton_Click"
                    ToolTip="فحص وتشخيص مشاكل البيانات"/>

            <Button x:Name="FixButton"
                    Content="🔧 إصلاح"
                    Click="FixButton_Click"
                    Background="#FF9800"
                    ToolTip="إصلاح المشاكل المكتشفة"/>

            <Button x:Name="CreateTestDataButton"
                    Content="🧪 بيانات تجريبية"
                    Click="CreateTestDataButton_Click"
                    Background="#9C27B0"
                    ToolTip="إنشاء بيانات تجريبية للاختبار"/>

            <Button x:Name="ClearButton"
                    Content="🗑️ مسح"
                    Click="ClearButton_Click"
                    Background="#f44336"
                    ToolTip="مسح التقرير"/>
        </StackPanel>

        <!-- منطقة التقرير -->
        <Border Grid.Row="2"
                BorderBrush="#ddd"
                BorderThickness="1"
                Margin="0,10,0,0">
            <ScrollViewer VerticalScrollBarVisibility="Auto"
                          HorizontalScrollBarVisibility="Auto">
                <TextBox x:Name="ReportTextBox"
                         IsReadOnly="True"
                         TextWrapping="Wrap"
                         FontFamily="Consolas"
                         FontSize="11"
                         Background="#f9f9f9"
                         BorderThickness="0"
                         Padding="10"
                         Text="اضغط على 'تشخيص' لبدء فحص قاعدة البيانات..."/>
            </ScrollViewer>
        </Border>

        <!-- شريط الحالة -->
        <StatusBar Grid.Row="3" Margin="0,10,0,0">
            <StatusBarItem>
                <TextBlock x:Name="StatusTextBlock"
                           Text="جاهز للفحص"
                           Foreground="#666"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <ProgressBar x:Name="ProgressBar"
                             Width="200"
                             Height="16"
                             Visibility="Collapsed"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
