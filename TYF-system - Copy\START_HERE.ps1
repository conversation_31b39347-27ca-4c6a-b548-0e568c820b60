# PowerShell Script for TYF Management System
# Encoding: UTF-8

# Set console encoding for Arabic text
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host ""
Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
Write-Host "║                    نظام TYF Management System                ║" -ForegroundColor Cyan
Write-Host "║                      البناء والتشغيل التلقائي                ║" -ForegroundColor Cyan
Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
Write-Host ""

Write-Host "🔍 فحص النظام..." -ForegroundColor Yellow
Write-Host ""

# Check .NET installation
Write-Host "📋 التحقق من تثبيت .NET SDK..." -ForegroundColor Yellow
try {
    $dotnetVersion = dotnet --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ .NET SDK مثبت - الإصدار: $dotnetVersion" -ForegroundColor Green
    } else {
        throw "Not installed"
    }
} catch {
    Write-Host "❌ .NET SDK غير مثبت!" -ForegroundColor Red
    Write-Host ""
    Write-Host "📥 يرجى تحميل وتثبيت .NET SDK من:" -ForegroundColor Yellow
    Write-Host "https://dotnet.microsoft.com/download" -ForegroundColor Blue
    Write-Host ""
    Read-Host "اضغط Enter للمتابعة"
    exit 1
}
Write-Host ""

# Check project directory
Write-Host "📁 التحقق من وجود مجلد المشروع..." -ForegroundColor Yellow
$projectPath = Join-Path $PSScriptRoot "TYFManagementSystem"
if (-not (Test-Path $projectPath)) {
    Write-Host "❌ مجلد TYFManagementSystem غير موجود!" -ForegroundColor Red
    Write-Host ""
    Write-Host "📍 المسار المتوقع: $projectPath" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "📂 المحتويات الحالية:" -ForegroundColor Yellow
    Get-ChildItem $PSScriptRoot | Select-Object Name | Format-Table -HideTableHeaders
    Write-Host ""
    Read-Host "اضغط Enter للمتابعة"
    exit 1
}

Write-Host "✅ مجلد المشروع موجود" -ForegroundColor Green
Write-Host ""

# Check project file
$projectFile = Join-Path $projectPath "TYFManagementSystem.csproj"
if (-not (Test-Path $projectFile)) {
    Write-Host "❌ ملف المشروع غير موجود!" -ForegroundColor Red
    Write-Host ""
    Write-Host "📍 المسار المتوقع: $projectFile" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "اضغط Enter للمتابعة"
    exit 1
}

Write-Host "✅ ملف المشروع موجود" -ForegroundColor Green
Write-Host ""

# Change to project directory
Write-Host "📂 الانتقال إلى مجلد المشروع..." -ForegroundColor Yellow
Set-Location $projectPath
Write-Host "✅ المسار الحالي: $(Get-Location)" -ForegroundColor Green
Write-Host ""

Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
Write-Host "║                         بدء البناء                          ║" -ForegroundColor Cyan
Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
Write-Host ""

# Clean project
Write-Host "🧹 تنظيف المشروع..." -ForegroundColor Yellow
dotnet clean --verbosity quiet
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في تنظيف المشروع!" -ForegroundColor Red
    Read-Host "اضغط Enter للمتابعة"
    exit 1
}
Write-Host "✅ تم تنظيف المشروع بنجاح" -ForegroundColor Green
Write-Host ""

# Restore packages
Write-Host "📦 استعادة الحزم والتبعيات..." -ForegroundColor Yellow
dotnet restore --verbosity quiet
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في استعادة الحزم!" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 جرب الأوامر التالية يدوياً:" -ForegroundColor Yellow
    Write-Host "   dotnet restore --force" -ForegroundColor Gray
    Write-Host "   dotnet restore --no-cache" -ForegroundColor Gray
    Write-Host ""
    Read-Host "اضغط Enter للمتابعة"
    exit 1
}
Write-Host "✅ تم استعادة الحزم بنجاح" -ForegroundColor Green
Write-Host ""

# Build project
Write-Host "🏗️ بناء المشروع..." -ForegroundColor Yellow
dotnet build --configuration Release --verbosity quiet
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في بناء المشروع!" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔍 تشغيل البناء مع تفاصيل أكثر..." -ForegroundColor Yellow
    dotnet build --configuration Release --verbosity normal
    Write-Host ""
    Read-Host "اضغط Enter للمتابعة"
    exit 1
}
Write-Host "✅ تم بناء المشروع بنجاح!" -ForegroundColor Green
Write-Host ""

Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
Write-Host "║                        تشغيل التطبيق                        ║" -ForegroundColor Cyan
Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
Write-Host ""

Write-Host "🚀 تشغيل نظام TYF Management System..." -ForegroundColor Yellow
Write-Host ""
Write-Host "💡 ملاحظات مهمة:" -ForegroundColor Cyan
Write-Host "   • سيفتح التطبيق في نافذة منفصلة" -ForegroundColor Gray
Write-Host "   • لإصلاح مشكلة بيانات المواقع، استخدم 'تشخيص قاعدة البيانات'" -ForegroundColor Gray
Write-Host "   • يمكنك إغلاق هذه النافذة بعد فتح التطبيق" -ForegroundColor Gray
Write-Host ""

Start-Sleep -Seconds 3

dotnet run --configuration Release

Write-Host ""
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في تشغيل التطبيق!" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔍 معلومات للمساعدة:" -ForegroundColor Yellow
    Write-Host "   • تأكد من عدم تشغيل نسخة أخرى من التطبيق" -ForegroundColor Gray
    Write-Host "   • تحقق من توفر مساحة كافية على القرص الصلب" -ForegroundColor Gray
    Write-Host "   • جرب تشغيل PowerShell كمدير" -ForegroundColor Gray
    Write-Host ""
} else {
    Write-Host "✅ تم إغلاق التطبيق بنجاح" -ForegroundColor Green
}

Write-Host ""
Write-Host "📋 انتهى التشغيل" -ForegroundColor Cyan
Read-Host "اضغط Enter للمتابعة"
