<Window x:Class="TYFManagementSystem.Views.IPTT.IndicatorUpdateDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="تحديث مؤشر الأداء" 
        Height="500" 
        Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Window.Resources>
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,0,0,5"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>
        
        <Style x:Key="TextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="8"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2E7D32"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="20,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="5"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#4CAF50"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1B5E20"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5">
        <Border Background="White" 
                CornerRadius="8" 
                Padding="30" 
                Margin="20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
            </Border.Effect>
            
            <StackPanel>
                <!-- Header -->
                <TextBlock Text="تحديث مؤشر الأداء" 
                         FontSize="18" 
                         FontWeight="Bold" 
                         Foreground="#2E7D32"
                         HorizontalAlignment="Center"
                         Margin="0,0,0,20"/>

                <!-- Indicator Number -->
                <TextBlock Text="رقم المؤشر:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="IndicatorNumberTextBox" Style="{StaticResource TextBoxStyle}"/>

                <!-- Indicator Name -->
                <TextBlock Text="اسم المؤشر:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="IndicatorNameTextBox" Style="{StaticResource TextBoxStyle}"/>

                <!-- Unit -->
                <TextBlock Text="الوحدة:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="UnitTextBox" Style="{StaticResource TextBoxStyle}"/>

                <!-- Target Value -->
                <TextBlock Text="القيمة المستهدفة:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="TargetValueTextBox" Style="{StaticResource TextBoxStyle}"/>

                <!-- Current Value -->
                <TextBlock Text="القيمة الحالية:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="CurrentValueTextBox" Style="{StaticResource TextBoxStyle}"/>

                <!-- Description -->
                <TextBlock Text="الوصف:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="DescriptionTextBox" 
                       Style="{StaticResource TextBoxStyle}"
                       Height="60"
                       TextWrapping="Wrap"
                       AcceptsReturn="True"
                       VerticalScrollBarVisibility="Auto"/>

                <!-- Data Source -->
                <TextBlock Text="مصدر البيانات:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="DataSourceTextBox" Style="{StaticResource TextBoxStyle}"/>

                <!-- Responsible Person -->
                <TextBlock Text="الشخص المسؤول:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="ResponsiblePersonTextBox" Style="{StaticResource TextBoxStyle}"/>

                <!-- Buttons -->
                <StackPanel Orientation="Horizontal" 
                          HorizontalAlignment="Center" 
                          Margin="0,20,0,0">
                    <Button Content="حفظ التحديثات" 
                          Style="{StaticResource ButtonStyle}"
                          Click="Save_Click"/>
                    <Button Content="إلغاء" 
                          Style="{StaticResource ButtonStyle}"
                          Background="#757575"
                          Click="Cancel_Click"/>
                </StackPanel>
            </StackPanel>
        </Border>
    </Grid>
</Window>
