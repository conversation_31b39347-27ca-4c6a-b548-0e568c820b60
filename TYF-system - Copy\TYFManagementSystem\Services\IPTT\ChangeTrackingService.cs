using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using TYFManagementSystem.Data;
using TYFManagementSystem.Models;
using TYFManagementSystem.Models.IPTT;
using System.Text.Json;
using System.Collections.ObjectModel;

namespace TYFManagementSystem.Services.IPTT
{
    /// <summary>
    /// خدمة تتبع التغييرات والإصدارات لبيانات IPTT
    /// </summary>
    public class ChangeTrackingService : IDisposable
    {
        #region Private Fields
        private readonly TyfDbContext _context;
        private readonly ChangeTrackingOptions _options;
        private bool _isDisposed = false;
        #endregion

        #region Constructor
        public ChangeTrackingService(ChangeTrackingOptions options = null)
        {
            _context = new TyfDbContext();
            _options = options ?? new ChangeTrackingOptions();
        }
        #endregion

        #region Change Tracking Methods
        /// <summary>
        /// تسجيل تغيير في خلية واحدة
        /// </summary>
        public async Task<bool> TrackCellChangeAsync(int projectId, int locationNumber, string indicatorNo,
                                                   string monthColumn, string oldValue, string newValue,
                                                   string changedBy = "System", string notes = "")
        {
            if (!_options.EnableChangeTracking) return true;

            try
            {
                var change = new IpttChangeHistory
                {
                    ProjectId = projectId,
                    LocationNumber = locationNumber,
                    IndicatorNo = indicatorNo,
                    MonthColumn = monthColumn,
                    OldValue = oldValue ?? "",
                    NewValue = newValue ?? "",
                    ChangeDate = DateTime.Now,
                    ChangedBy = changedBy,
                    ChangeType = ChangeTypes.Update,
                    Notes = notes
                };

                _context.Set<IpttChangeHistory>().Add(change);
                await _context.SaveChangesAsync();

                // تنظيف التاريخ القديم
                await CleanupOldHistoryAsync(projectId);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تسجيل التغيير: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تسجيل تغييرات متعددة
        /// </summary>
        public async Task<bool> TrackBulkChangesAsync(int projectId, List<CellChange> changes, 
                                                     string changedBy = "System", string notes = "")
        {
            if (!_options.EnableChangeTracking || !changes.Any()) return true;

            try
            {
                var historyItems = changes.Select(change => new IpttChangeHistory
                {
                    ProjectId = projectId,
                    LocationNumber = change.LocationNumber,
                    IndicatorNo = change.IndicatorNo,
                    MonthColumn = change.MonthColumn,
                    OldValue = change.OldValue ?? "",
                    NewValue = change.NewValue ?? "",
                    ChangeDate = DateTime.Now,
                    ChangedBy = changedBy,
                    ChangeType = ChangeTypes.BulkUpdate,
                    Notes = notes
                }).ToList();

                _context.Set<IpttChangeHistory>().AddRange(historyItems);
                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تسجيل التغييرات المتعددة: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تسجيل إضافة أو حذف موقع
        /// </summary>
        public async Task<bool> TrackLocationChangeAsync(int projectId, int locationNumber, 
                                                       string changeType, string changedBy = "System")
        {
            if (!_options.EnableChangeTracking) return true;

            try
            {
                var change = new IpttChangeHistory
                {
                    ProjectId = projectId,
                    LocationNumber = locationNumber,
                    IndicatorNo = "LOCATION",
                    MonthColumn = "ALL",
                    OldValue = changeType == ChangeTypes.LocationAdd ? "" : $"الموقع {locationNumber}",
                    NewValue = changeType == ChangeTypes.LocationAdd ? $"الموقع {locationNumber}" : "",
                    ChangeDate = DateTime.Now,
                    ChangedBy = changedBy,
                    ChangeType = changeType,
                    Notes = changeType == ChangeTypes.LocationAdd ? "إضافة موقع جديد" : "حذف موقع"
                };

                _context.Set<IpttChangeHistory>().Add(change);
                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تسجيل تغيير الموقع: {ex.Message}");
                return false;
            }
        }
        #endregion

        #region Version Management
        /// <summary>
        /// إنشاء إصدار جديد من بيانات المشروع
        /// </summary>
        public async Task<int> CreateVersionAsync(int projectId, string versionName, string description,
                                                ObservableCollection<IpttIndicator> indicators,
                                                List<string> monthColumns,
                                                Dictionary<int, ObservableCollection<IpttDisplayRow>> locationData,
                                                string createdBy = "System")
        {
            if (!_options.EnableVersioning) return -1;

            try
            {
                // الحصول على رقم الإصدار التالي
                var lastVersion = await _context.Set<IpttVersion>()
                    .Where(v => v.ProjectId == projectId)
                    .OrderByDescending(v => v.VersionNumber)
                    .FirstOrDefaultAsync();

                var nextVersionNumber = (lastVersion?.VersionNumber ?? 0) + 1;

                // تسلسل البيانات
                var versionData = new
                {
                    Indicators = indicators,
                    MonthColumns = monthColumns,
                    LocationData = locationData,
                    CreatedDate = DateTime.Now
                };

                var serializedData = JsonSerializer.Serialize(versionData, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                // إنشاء الإصدار الجديد
                var version = new IpttVersion
                {
                    ProjectId = projectId,
                    VersionNumber = nextVersionNumber,
                    VersionName = versionName,
                    Description = description,
                    CreatedDate = DateTime.Now,
                    CreatedBy = createdBy,
                    IsActive = true,
                    SerializedData = serializedData
                };

                // إلغاء تفعيل الإصدارات السابقة
                var previousVersions = await _context.Set<IpttVersion>()
                    .Where(v => v.ProjectId == projectId && v.IsActive)
                    .ToListAsync();

                foreach (var prev in previousVersions)
                {
                    prev.IsActive = false;
                }

                _context.Set<IpttVersion>().Add(version);
                await _context.SaveChangesAsync();

                // تنظيف الإصدارات القديمة
                await CleanupOldVersionsAsync(projectId);

                return version.Id;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء الإصدار: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// الحصول على قائمة الإصدارات
        /// </summary>
        public async Task<List<VersionItem>> GetVersionsAsync(int projectId)
        {
            try
            {
                var versions = await _context.Set<IpttVersion>()
                    .Where(v => v.ProjectId == projectId)
                    .OrderByDescending(v => v.CreatedDate)
                    .Select(v => new VersionItem
                    {
                        Id = v.Id,
                        VersionNumber = v.VersionNumber,
                        VersionName = v.VersionName,
                        Description = v.Description,
                        CreatedDate = v.CreatedDate,
                        CreatedBy = v.CreatedBy,
                        IsActive = v.IsActive
                    })
                    .ToListAsync();

                return versions;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على الإصدارات: {ex.Message}");
                return new List<VersionItem>();
            }
        }

        /// <summary>
        /// استعادة إصدار معين
        /// </summary>
        public async Task<RestoreResult> RestoreVersionAsync(int versionId, string restoredBy = "System")
        {
            try
            {
                var version = await _context.Set<IpttVersion>()
                    .FirstOrDefaultAsync(v => v.Id == versionId);

                if (version == null)
                {
                    return new RestoreResult
                    {
                        Success = false,
                        ErrorMessage = "الإصدار المطلوب غير موجود"
                    };
                }

                // إلغاء تفعيل الإصدارات الحالية
                var currentVersions = await _context.Set<IpttVersion>()
                    .Where(v => v.ProjectId == version.ProjectId && v.IsActive)
                    .ToListAsync();

                foreach (var current in currentVersions)
                {
                    current.IsActive = false;
                }

                // تفعيل الإصدار المطلوب
                version.IsActive = true;

                await _context.SaveChangesAsync();

                // تسجيل عملية الاستعادة
                await TrackVersionRestoreAsync(version.ProjectId, versionId, restoredBy);

                return new RestoreResult
                {
                    Success = true,
                    Message = $"تم استعادة الإصدار {version.VersionName} بنجاح",
                    RestoreDate = DateTime.Now,
                    RestoredChanges = 1
                };
            }
            catch (Exception ex)
            {
                return new RestoreResult
                {
                    Success = false,
                    ErrorMessage = $"خطأ في استعادة الإصدار: {ex.Message}"
                };
            }
        }
        #endregion

        #region History Queries
        /// <summary>
        /// البحث في تاريخ التغييرات
        /// </summary>
        public async Task<ChangeHistoryResult> GetChangeHistoryAsync(ChangeHistoryFilter filter)
        {
            try
            {
                var query = _context.Set<IpttChangeHistory>().AsQueryable();

                // تطبيق الفلاتر
                if (filter.ProjectId.HasValue)
                    query = query.Where(h => h.ProjectId == filter.ProjectId.Value);

                if (filter.LocationNumber.HasValue)
                    query = query.Where(h => h.LocationNumber == filter.LocationNumber.Value);

                if (!string.IsNullOrEmpty(filter.IndicatorNo))
                    query = query.Where(h => h.IndicatorNo.Contains(filter.IndicatorNo));

                if (!string.IsNullOrEmpty(filter.MonthColumn))
                    query = query.Where(h => h.MonthColumn == filter.MonthColumn);

                if (filter.FromDate.HasValue)
                    query = query.Where(h => h.ChangeDate >= filter.FromDate.Value);

                if (filter.ToDate.HasValue)
                    query = query.Where(h => h.ChangeDate <= filter.ToDate.Value);

                if (!string.IsNullOrEmpty(filter.ChangeType))
                    query = query.Where(h => h.ChangeType == filter.ChangeType);

                if (!string.IsNullOrEmpty(filter.ChangedBy))
                    query = query.Where(h => h.ChangedBy.Contains(filter.ChangedBy));

                // الحصول على العدد الإجمالي
                var totalCount = await query.CountAsync();

                // تطبيق الترقيم
                var items = await query
                    .OrderByDescending(h => h.ChangeDate)
                    .Skip((filter.PageNumber - 1) * filter.PageSize)
                    .Take(filter.PageSize)
                    .Select(h => new ChangeHistoryItem
                    {
                        Id = h.Id,
                        ChangeDate = h.ChangeDate,
                        LocationName = $"الموقع {h.LocationNumber}",
                        IndicatorName = h.IndicatorNo,
                        MonthColumn = h.MonthColumn,
                        OldValue = h.OldValue,
                        NewValue = h.NewValue,
                        ChangedBy = h.ChangedBy,
                        ChangeType = h.ChangeType,
                        Notes = h.Notes
                    })
                    .ToListAsync();

                return new ChangeHistoryResult
                {
                    Items = items,
                    TotalCount = totalCount,
                    PageNumber = filter.PageNumber,
                    PageSize = filter.PageSize
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث في التاريخ: {ex.Message}");
                return new ChangeHistoryResult();
            }
        }
        #endregion

        #region Private Methods
        private async Task CleanupOldHistoryAsync(int projectId)
        {
            if (_options.MaxHistoryDays <= 0) return;

            try
            {
                var cutoffDate = DateTime.Now.AddDays(-_options.MaxHistoryDays);
                var oldHistory = await _context.Set<IpttChangeHistory>()
                    .Where(h => h.ProjectId == projectId && h.ChangeDate < cutoffDate)
                    .ToListAsync();

                if (oldHistory.Any())
                {
                    _context.Set<IpttChangeHistory>().RemoveRange(oldHistory);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنظيف التاريخ القديم: {ex.Message}");
            }
        }

        private async Task CleanupOldVersionsAsync(int projectId)
        {
            if (_options.MaxVersions <= 0) return;

            try
            {
                var versions = await _context.Set<IpttVersion>()
                    .Where(v => v.ProjectId == projectId)
                    .OrderByDescending(v => v.CreatedDate)
                    .ToListAsync();

                if (versions.Count > _options.MaxVersions)
                {
                    var versionsToDelete = versions.Skip(_options.MaxVersions).ToList();
                    _context.Set<IpttVersion>().RemoveRange(versionsToDelete);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنظيف الإصدارات القديمة: {ex.Message}");
            }
        }

        private async Task TrackVersionRestoreAsync(int projectId, int versionId, string restoredBy)
        {
            try
            {
                var change = new IpttChangeHistory
                {
                    ProjectId = projectId,
                    LocationNumber = 0,
                    IndicatorNo = "VERSION_RESTORE",
                    MonthColumn = "ALL",
                    OldValue = "",
                    NewValue = versionId.ToString(),
                    ChangeDate = DateTime.Now,
                    ChangedBy = restoredBy,
                    ChangeType = "VersionRestore",
                    Notes = $"تم استعادة الإصدار رقم {versionId}"
                };

                _context.Set<IpttChangeHistory>().Add(change);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تسجيل استعادة الإصدار: {ex.Message}");
            }
        }
        #endregion

        #region IDisposable Implementation
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_isDisposed && disposing)
            {
                _context?.Dispose();
                _isDisposed = true;
            }
        }
        #endregion
    }

    /// <summary>
    /// نموذج تغيير خلية
    /// </summary>
    public class CellChange
    {
        public int LocationNumber { get; set; }
        public string IndicatorNo { get; set; } = "";
        public string MonthColumn { get; set; } = "";
        public string OldValue { get; set; } = "";
        public string NewValue { get; set; } = "";
    }
}
