using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace TYFManagementSystem.Models
{
    /// <summary>
    /// نموذج ملخص المشروع للعرض في واجهة إدارة المشاريع
    /// </summary>
    public class ProjectSummary : INotifyPropertyChanged
    {
        private Project _project;
        private double _completionPercentage;
        private int _totalIndicators;
        private int _completedIndicators;
        private int _locationCount;
        private DateTime _lastUpdated;
        private string _progressStatus = string.Empty;

        public ProjectSummary(Project project)
        {
            _project = project;
            _lastUpdated = DateTime.Now;
            UpdateSummary();
        }

        public Project Project
        {
            get => _project;
            set => SetProperty(ref _project, value);
        }

        public double CompletionPercentage
        {
            get => _completionPercentage;
            set => SetProperty(ref _completionPercentage, value);
        }

        public int TotalIndicators
        {
            get => _totalIndicators;
            set => SetProperty(ref _totalIndicators, value);
        }

        public int CompletedIndicators
        {
            get => _completedIndicators;
            set => SetProperty(ref _completedIndicators, value);
        }

        public int LocationCount
        {
            get => _locationCount;
            set => SetProperty(ref _locationCount, value);
        }

        public DateTime LastUpdated
        {
            get => _lastUpdated;
            set => SetProperty(ref _lastUpdated, value);
        }

        public string ProgressStatus
        {
            get => _progressStatus;
            set => SetProperty(ref _progressStatus, value);
        }

        // Computed Properties
        public string CompletionText => $"{CompletionPercentage:F1}%";

        public string IndicatorsText => $"{CompletedIndicators}/{TotalIndicators}";

        public string LocationsText => LocationCount > 0 ? $"{LocationCount} موقع" : "لا توجد مواقع";

        public string StatusColor => Project.Status switch
        {
            "نشط" => "#4CAF50",
            "مخطط" => "#FF9800", 
            "مكتمل" => "#2196F3",
            "متوقف" => "#F44336",
            "مؤجل" => "#9C27B0",
            _ => "#757575"
        };

        public string ProgressColor => CompletionPercentage switch
        {
            >= 90 => "#4CAF50",  // أخضر
            >= 70 => "#8BC34A",  // أخضر فاتح
            >= 50 => "#FF9800",  // برتقالي
            >= 30 => "#FF5722",  // برتقالي محمر
            _ => "#F44336"       // أحمر
        };

        public string RemainingTimeText
        {
            get
            {
                var remaining = Project.EndDate - DateTime.Now;
                if (remaining.TotalDays < 0)
                    return "منتهي";
                else if (remaining.TotalDays < 7)
                    return $"{(int)remaining.TotalDays} أيام";
                else if (remaining.TotalDays < 30)
                    return $"{(int)(remaining.TotalDays / 7)} أسابيع";
                else if (remaining.TotalDays < 365)
                    return $"{(int)(remaining.TotalDays / 30)} أشهر";
                else
                    return $"{(int)(remaining.TotalDays / 365)} سنوات";
            }
        }

        public bool IsOverdue => Project.EndDate < DateTime.Now && Project.Status != "مكتمل";

        public bool IsNearDeadline => (Project.EndDate - DateTime.Now).TotalDays <= 30 && Project.Status != "مكتمل";

        /// <summary>
        /// تحديث ملخص المشروع من بيانات IPTT
        /// </summary>
        public void UpdateSummary()
        {
            // هذه الطريقة ستحدث لاحقاً لتحميل البيانات من قاعدة البيانات
            UpdateProgressStatus();
            LastUpdated = DateTime.Now;
        }

        /// <summary>
        /// تحديث حالة التقدم بناءً على النسبة المئوية
        /// </summary>
        private void UpdateProgressStatus()
        {
            ProgressStatus = CompletionPercentage switch
            {
                >= 100 => "مكتمل",
                >= 90 => "شبه مكتمل",
                >= 75 => "متقدم جداً",
                >= 50 => "متقدم",
                >= 25 => "في التقدم",
                > 0 => "بدء العمل",
                _ => "لم يبدأ"
            };
        }

        /// <summary>
        /// تحديث البيانات من خدمة IPTT
        /// </summary>
        /// <param name="totalIndicators">إجمالي المؤشرات</param>
        /// <param name="completedIndicators">المؤشرات المكتملة</param>
        /// <param name="locationCount">عدد المواقع</param>
        /// <param name="completionPercentage">النسبة المئوية للإنجاز</param>
        public void UpdateFromIpttData(int totalIndicators, int completedIndicators, int locationCount, double completionPercentage)
        {
            TotalIndicators = totalIndicators;
            CompletedIndicators = completedIndicators;
            LocationCount = locationCount;
            CompletionPercentage = completionPercentage;
            UpdateProgressStatus();
            LastUpdated = DateTime.Now;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected virtual bool SetProperty<T>(ref T storage, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(storage, value))
                return false;

            storage = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}
