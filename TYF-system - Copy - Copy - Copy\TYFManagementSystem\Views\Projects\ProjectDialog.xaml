<Window x:Class="TYFManagementSystem.Views.Projects.ProjectDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة المشروع" 
        Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F5F5F5"
        FlowDirection="RightToLeft">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
            </Border.Effect>
            <TextBlock x:Name="HeaderText" 
                     Text="إضافة مشروع جديد" 
                     FontSize="20" 
                     FontWeight="Bold" 
                     Foreground="#2E7D32"
                     HorizontalAlignment="Center"/>
        </Border>

        <!-- Form -->
        <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="30">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
            </Border.Effect>
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- Project Number -->
                    <TextBlock Text="رقم المشروع:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="ProjectNumberTextBox"
                           Padding="10"
                           FontSize="14"
                           Margin="0,0,0,15"
                           BorderBrush="#E0E0E0"
                           BorderThickness="1"
                           IsReadOnly="True"
                           Background="#F5F5F5"
                           Text="سيتم توليده تلقائياً"/>

                    <!-- Project Code -->
                    <TextBlock Text="كود المشروع:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="ProjectCodeTextBox"
                           Padding="10"
                           FontSize="14"
                           Margin="0,0,0,15"
                           BorderBrush="#E0E0E0"
                           BorderThickness="1"/>

                    <!-- Project Name -->
                    <TextBlock Text="اسم المشروع:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="NameTextBox"
                           Padding="10"
                           FontSize="14"
                           Margin="0,0,0,15"
                           BorderBrush="#E0E0E0"
                           BorderThickness="1"/>

                    <!-- Region -->
                    <TextBlock Text="منطقة المشروع:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="RegionTextBox"
                           Padding="10"
                           FontSize="14"
                           Margin="0,0,0,15"
                           BorderBrush="#E0E0E0"
                           BorderThickness="1"/>

                    <!-- Description -->
                    <TextBlock Text="وصف المشروع:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="DescriptionTextBox" 
                           Padding="10" 
                           FontSize="14" 
                           Height="80"
                           TextWrapping="Wrap"
                           AcceptsReturn="True"
                           VerticalScrollBarVisibility="Auto"
                           Margin="0,0,0,15"
                           BorderBrush="#E0E0E0"
                           BorderThickness="1"/>

                    <!-- Start Date -->
                    <TextBlock Text="تاريخ البداية:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <DatePicker x:Name="StartDatePicker" 
                              Padding="10" 
                              FontSize="14" 
                              Margin="0,0,0,15"
                              BorderBrush="#E0E0E0"
                              BorderThickness="1"/>

                    <!-- End Date -->
                    <TextBlock Text="تاريخ النهاية:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <DatePicker x:Name="EndDatePicker" 
                              Padding="10" 
                              FontSize="14" 
                              Margin="0,0,0,15"
                              BorderBrush="#E0E0E0"
                              BorderThickness="1"/>

                    <!-- Status -->
                    <TextBlock Text="حالة المشروع:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <ComboBox x:Name="StatusComboBox" 
                            Padding="10" 
                            FontSize="14" 
                            Margin="0,0,0,15"
                            BorderBrush="#E0E0E0"
                            BorderThickness="1">
                        <ComboBoxItem Content="مخطط"/>
                        <ComboBoxItem Content="نشط"/>
                        <ComboBoxItem Content="متوقف"/>
                        <ComboBoxItem Content="مكتمل"/>
                        <ComboBoxItem Content="ملغي"/>
                    </ComboBox>

                    <!-- Budget -->
                    <TextBlock Text="الميزانية:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="BudgetTextBox" 
                           Padding="10" 
                           FontSize="14" 
                           Margin="0,0,0,15"
                           BorderBrush="#E0E0E0"
                           BorderThickness="1"/>

                    <!-- Manager -->
                    <TextBlock Text="مدير المشروع:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="ManagerTextBox" 
                           Padding="10" 
                           FontSize="14" 
                           Margin="0,0,0,15"
                           BorderBrush="#E0E0E0"
                           BorderThickness="1"/>

                    <!-- Beneficiaries -->
                    <TextBlock Text="عدد المستفيدين:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="BeneficiariesTextBox" 
                           Padding="10" 
                           FontSize="14" 
                           Margin="0,0,0,15"
                           BorderBrush="#E0E0E0"
                           BorderThickness="1"/>
                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="White" CornerRadius="8" Padding="20" Margin="0,20,0,0">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
            </Border.Effect>
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="حفظ"
                        Click="SaveButton_Click"
                        Background="#2E7D32"
                        Foreground="White"
                        Padding="30,10"
                        Margin="10,0"
                        FontSize="14"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                CornerRadius="5"
                                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>

                <Button Content="إلغاء"
                        Click="CancelButton_Click"
                        Background="#757575"
                        Foreground="White"
                        Padding="30,10"
                        Margin="10,0"
                        FontSize="14"
                        FontWeight="Bold"
                        BorderThickness="0"
                        Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                CornerRadius="5"
                                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
