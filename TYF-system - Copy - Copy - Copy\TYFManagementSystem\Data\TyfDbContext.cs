using Microsoft.EntityFrameworkCore;
using TYFManagementSystem.Models.Database;
using TYFManagementSystem.Models;
using System.IO;

namespace TYFManagementSystem.Data
{
    /// <summary>
    /// سياق قاعدة البيانات الرئيسي لنظام TYF
    /// </summary>
    public class TyfDbContext : DbContext
    {
        // Main Projects Table
        public DbSet<ProjectDb> Projects { get; set; }

        // IPTT Tables
        public DbSet<IpttProjectDb> IpttProjects { get; set; }
        public DbSet<IpttIndicatorDb> IpttIndicators { get; set; }
        public DbSet<IpttDataTypeDb> IpttDataTypes { get; set; }
        public DbSet<IpttMonthlyDataDb> IpttMonthlyData { get; set; }
        public DbSet<IpttMonthColumnDb> IpttMonthColumns { get; set; }
        public DbSet<IpttLocationDataDb> IpttLocationData { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                // استخدام SQLite كقاعدة بيانات افتراضية
                var dbPath = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "TYFManagementSystem",
                    "TYF_Database.db"
                );

                // إنشاء المجلد إذا لم يكن موجوداً
                var directory = Path.GetDirectoryName(dbPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory!);
                }

                optionsBuilder.UseSqlite($"Data Source={dbPath}");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // تكوين العلاقات والقيود
            ConfigureProjectRelationships(modelBuilder);
            ConfigureIpttRelationships(modelBuilder);
        }

        private void ConfigureProjectRelationships(ModelBuilder modelBuilder)
        {
            // تكوين جدول المشاريع
            modelBuilder.Entity<ProjectDb>()
                .HasIndex(p => p.ProjectNumber)
                .IsUnique();

            modelBuilder.Entity<ProjectDb>()
                .HasIndex(p => p.ProjectCode);

            modelBuilder.Entity<ProjectDb>()
                .Property(p => p.Budget)
                .HasPrecision(18, 2);
        }

        private void ConfigureIpttRelationships(ModelBuilder modelBuilder)
        {
            // IpttProject relationships
            modelBuilder.Entity<IpttProjectDb>()
                .HasMany(p => p.Indicators)
                .WithOne(i => i.Project)
                .HasForeignKey(i => i.ProjectId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<IpttProjectDb>()
                .HasMany(p => p.MonthColumns)
                .WithOne(m => m.Project)
                .HasForeignKey(m => m.ProjectId)
                .OnDelete(DeleteBehavior.Cascade);

            // IpttIndicator relationships
            modelBuilder.Entity<IpttIndicatorDb>()
                .HasMany(i => i.DataTypes)
                .WithOne(d => d.Indicator)
                .HasForeignKey(d => d.IndicatorId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<IpttIndicatorDb>()
                .HasMany(i => i.MonthlyData)
                .WithOne(m => m.Indicator)
                .HasForeignKey(m => m.IndicatorId)
                .OnDelete(DeleteBehavior.Cascade);

            // IpttDataType relationships
            modelBuilder.Entity<IpttDataTypeDb>()
                .HasMany(d => d.MonthlyData)
                .WithOne(m => m.DataType)
                .HasForeignKey(m => m.DataTypeId)
                .OnDelete(DeleteBehavior.Cascade);

            // IpttLocationData relationships
            modelBuilder.Entity<IpttLocationDataDb>()
                .HasOne(l => l.Project)
                .WithMany()
                .HasForeignKey(l => l.ProjectId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<IpttLocationDataDb>()
                .HasOne(l => l.Indicator)
                .WithMany()
                .HasForeignKey(l => l.IndicatorId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<IpttLocationDataDb>()
                .HasOne(l => l.DataType)
                .WithMany()
                .HasForeignKey(l => l.DataTypeId)
                .OnDelete(DeleteBehavior.SetNull);

            // Indexes for better performance
            modelBuilder.Entity<IpttProjectDb>()
                .HasIndex(p => p.ProjectId)
                .IsUnique();

            modelBuilder.Entity<IpttIndicatorDb>()
                .HasIndex(i => new { i.ProjectId, i.IndicatorNumber })
                .IsUnique();

            modelBuilder.Entity<IpttMonthlyDataDb>()
                .HasIndex(m => new { m.IndicatorId, m.DataTypeId, m.MonthColumn });

            modelBuilder.Entity<IpttLocationDataDb>()
                .HasIndex(l => new { l.ProjectId, l.LocationNumber, l.IndicatorId, l.MonthColumn });
        }

        /// <summary>
        /// إنشاء قاعدة البيانات والجداول إذا لم تكن موجودة
        /// </summary>
        public async Task EnsureDatabaseCreatedAsync()
        {
            try
            {
                await Database.EnsureCreatedAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في إنشاء قاعدة البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تطبيق التحديثات المعلقة على قاعدة البيانات
        /// </summary>
        public async Task MigrateDatabaseAsync()
        {
            try
            {
                await Database.MigrateAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تطبيق تحديثات قاعدة البيانات: {ex.Message}", ex);
            }
        }
    }
}
