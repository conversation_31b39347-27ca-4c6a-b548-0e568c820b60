using System;
using System.Collections.ObjectModel;
using System.Windows.Input;
using TYFManagementSystem.Commands;
using TYFManagementSystem.Models;
using TYFManagementSystem.Services;
using TYFManagementSystem.Services.IPTT;
using System.Windows;
using System.ComponentModel;
using System.Windows.Data;
using System.Linq;
using Microsoft.Win32;
using System.IO;
using System.Threading.Tasks;

namespace TYFManagementSystem.ViewModels.Projects
{
    public class ProjectsViewModel : BaseViewModel
    {
        private readonly ProjectService _projectService;
        private readonly CsvExportService _excelExportService;
        private readonly IpttDatabaseService _ipttDatabaseService;
        private Project? _selectedProject;
        private ObservableCollection<Project> _projects;
        private ObservableCollection<ProjectSummary> _projectSummaries;
        private ICollectionView _projectsView;
        private string _searchText = string.Empty;
        private string _selectedStatusFilter = "الكل";
        private string _selectedRegionFilter = "الكل";
        private string _selectedDonorFilter = "الكل";
        private DateTime? _startDateFilter;
        private DateTime? _endDateFilter;
        private bool _showSummaryView = true;

        public ProjectsViewModel()
        {
            _projectService = ProjectService.Instance;
            _excelExportService = new CsvExportService();
            _ipttDatabaseService = new IpttDatabaseService();
            _projects = _projectService.GetAllProjects();
            _projectSummaries = new ObservableCollection<ProjectSummary>();

            // Initialize CollectionView for filtering and sorting
            _projectsView = CollectionViewSource.GetDefaultView(_projects);
            _projectsView.Filter = ProjectFilter;

            InitializeCommands();
            InitializeFilters();
            LoadProjectSummariesAsync();
        }

        public ObservableCollection<Project> Projects
        {
            get => _projects;
            set => SetProperty(ref _projects, value);
        }

        public ObservableCollection<ProjectSummary> ProjectSummaries
        {
            get => _projectSummaries;
            set => SetProperty(ref _projectSummaries, value);
        }

        public ICollectionView ProjectsView
        {
            get => _projectsView;
            set => SetProperty(ref _projectsView, value);
        }

        public Project? SelectedProject
        {
            get => _selectedProject;
            set => SetProperty(ref _selectedProject, value);
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    ApplyFilter();
                }
            }
        }

        public string SelectedStatusFilter
        {
            get => _selectedStatusFilter;
            set
            {
                if (SetProperty(ref _selectedStatusFilter, value))
                {
                    ApplyFilter();
                }
            }
        }

        public string SelectedRegionFilter
        {
            get => _selectedRegionFilter;
            set
            {
                if (SetProperty(ref _selectedRegionFilter, value))
                {
                    ApplyFilter();
                }
            }
        }

        public string SelectedDonorFilter
        {
            get => _selectedDonorFilter;
            set
            {
                if (SetProperty(ref _selectedDonorFilter, value))
                {
                    ApplyFilter();
                }
            }
        }

        public DateTime? StartDateFilter
        {
            get => _startDateFilter;
            set
            {
                if (SetProperty(ref _startDateFilter, value))
                {
                    ApplyFilter();
                }
            }
        }

        public DateTime? EndDateFilter
        {
            get => _endDateFilter;
            set
            {
                if (SetProperty(ref _endDateFilter, value))
                {
                    ApplyFilter();
                }
            }
        }

        public bool ShowSummaryView
        {
            get => _showSummaryView;
            set => SetProperty(ref _showSummaryView, value);
        }

        public ObservableCollection<string> StatusFilters { get; set; } = new();
        public ObservableCollection<string> RegionFilters { get; set; } = new();
        public ObservableCollection<string> DonorFilters { get; set; } = new();

        // Commands
        public ICommand AddProjectCommand { get; private set; } = null!;
        public ICommand EditProjectCommand { get; private set; } = null!;
        public ICommand DeleteProjectCommand { get; private set; } = null!;
        public ICommand ExportToExcelCommand { get; private set; } = null!;
        public ICommand SaveToDatabaseCommand { get; private set; } = null!;
        public ICommand SortCommand { get; private set; } = null!;
        public ICommand DesignIPTTCommand { get; private set; } = null!;
        public ICommand OpenEmbeddedIPTTCommand { get; private set; } = null!;
        public ICommand RefreshSummariesCommand { get; private set; } = null!;
        public ICommand ClearFiltersCommand { get; private set; } = null!;

        private void InitializeCommands()
        {
            AddProjectCommand = new RelayCommand(AddProject);
            EditProjectCommand = new RelayCommand(EditProject, CanEditProject);
            DeleteProjectCommand = new RelayCommand(DeleteProject, CanDeleteProject);
            ExportToExcelCommand = new RelayCommand(ExportToExcel);
            SaveToDatabaseCommand = new RelayCommand(SaveToDatabase);
            SortCommand = new RelayCommand(SortProjects);
            DesignIPTTCommand = new RelayCommand(DesignIPTT);
            OpenEmbeddedIPTTCommand = new RelayCommand(OpenEmbeddedIPTT, CanOpenIPTT);
            RefreshSummariesCommand = new RelayCommand(async () => await LoadProjectSummariesAsync());
            ClearFiltersCommand = new RelayCommand(ClearFilters);
        }

        private void InitializeFilters()
        {
            StatusFilters.Add("الكل");
            StatusFilters.Add("نشط");
            StatusFilters.Add("مكتمل");
            StatusFilters.Add("مخطط");
            StatusFilters.Add("متوقف");
            StatusFilters.Add("مؤجل");
            StatusFilters.Add("ملغي");

            RegionFilters.Add("الكل");
            var regions = _projects.Select(p => p.Region).Distinct().Where(r => !string.IsNullOrEmpty(r));
            foreach (var region in regions)
            {
                RegionFilters.Add(region);
            }

            DonorFilters.Add("الكل");
            var donors = _projects.Select(p => p.Donor).Distinct().Where(d => !string.IsNullOrEmpty(d));
            foreach (var donor in donors)
            {
                DonorFilters.Add(donor);
            }
        }

        private bool ProjectFilter(object item)
        {
            if (item is not Project project) return false;

            // Search filter
            if (!string.IsNullOrEmpty(SearchText))
            {
                var searchLower = SearchText.ToLower();
                if (!project.Name.ToLower().Contains(searchLower) &&
                    !project.ProjectNumber.ToLower().Contains(searchLower) &&
                    !project.ProjectCode.ToLower().Contains(searchLower) &&
                    !project.Region.ToLower().Contains(searchLower) &&
                    !project.Manager.ToLower().Contains(searchLower) &&
                    !project.Donor.ToLower().Contains(searchLower))
                {
                    return false;
                }
            }

            // Status filter
            if (SelectedStatusFilter != "الكل" && project.Status != SelectedStatusFilter)
            {
                return false;
            }

            // Region filter
            if (SelectedRegionFilter != "الكل" && project.Region != SelectedRegionFilter)
            {
                return false;
            }

            // Donor filter
            if (SelectedDonorFilter != "الكل" && project.Donor != SelectedDonorFilter)
            {
                return false;
            }

            // Date range filters
            if (StartDateFilter.HasValue && project.StartDate < StartDateFilter.Value)
            {
                return false;
            }

            if (EndDateFilter.HasValue && project.EndDate > EndDateFilter.Value)
            {
                return false;
            }

            return true;
        }

        private void ApplyFilter()
        {
            _projectsView.Refresh();
        }

        private void AddProject()
        {
            var newProject = new Project
            {
                Name = "مشروع جديد",
                ProjectCode = "",
                Region = "",
                Description = "وصف المشروع",
                StartDate = DateTime.Now,
                EndDate = DateTime.Now.AddMonths(6),
                Status = "مخطط",
                Budget = 0,
                Manager = "غير محدد",
                Beneficiaries = 0
            };

            var dialog = new Views.Projects.ProjectDialog(newProject, true);
            if (dialog.ShowDialog() == true)
            {
                try
                {
                    _projectService.AddProject(newProject);
                    RefreshProjects();
                    MessageBox.Show("تم إضافة المشروع بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"فشل في إضافة المشروع: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void EditProject()
        {
            if (SelectedProject == null)
            {
                MessageBox.Show("يرجى اختيار مشروع للتعديل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // رسالة تأكيد التعديل
            var result = MessageBox.Show(
                $"هل تريد أن تقوم بتعديل '{SelectedProject.Name}'؟",
                "تأكيد التعديل",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result != MessageBoxResult.Yes) return;

            var projectCopy = new Project
            {
                Id = SelectedProject.Id,
                ProjectNumber = SelectedProject.ProjectNumber,
                ProjectCode = SelectedProject.ProjectCode,
                Name = SelectedProject.Name,
                Region = SelectedProject.Region,
                Description = SelectedProject.Description,
                StartDate = SelectedProject.StartDate,
                EndDate = SelectedProject.EndDate,
                Status = SelectedProject.Status,
                Budget = SelectedProject.Budget,
                Manager = SelectedProject.Manager,
                Beneficiaries = SelectedProject.Beneficiaries
            };

            var dialog = new Views.Projects.ProjectDialog(projectCopy, false);
            if (dialog.ShowDialog() == true)
            {
                try
                {
                    _projectService.UpdateProject(projectCopy);
                    RefreshProjects();
                    MessageBox.Show("تم تحديث المشروع بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"فشل في تحديث المشروع: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void DeleteProject()
        {
            if (SelectedProject == null) return;

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف المشروع '{SelectedProject.Name}'؟",
                "تأكيد الحذف",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    _projectService.DeleteProject(SelectedProject.Id);
                    RefreshProjects();
                    SelectedProject = null;
                    MessageBox.Show("تم حذف المشروع بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"فشل في حذف المشروع: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private bool CanEditProject()
        {
            return SelectedProject != null;
        }

        private bool CanDeleteProject()
        {
            return SelectedProject != null;
        }

        /// <summary>
        /// تصدير المشاريع إلى ملف Excel
        /// </summary>
        private void ExportToExcel()
        {
            try
            {
                // إظهار نافذة حفظ الملف
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "حفظ تقرير المشاريع",
                    Filter = "ملفات Excel (*.xlsx)|*.xlsx",
                    DefaultExt = "xlsx",
                    FileName = $"تقرير_المشاريع_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.xlsx"
                };

                // إذا اختار المستخدم مكان الحفظ
                if (saveFileDialog.ShowDialog() == true)
                {
                    // الحصول على المشاريع المفلترة (ما يراه المستخدم حالياً)
                    var projectsToExport = _projectsView.Cast<Project>().ToList();

                    // تصدير إلى Excel
                    _excelExportService.ExportProjectsToExcel(projectsToExport, saveFileDialog.FileName);

                    // إظهار رسالة نجاح مع خيار فتح الملف
                    var result = MessageBox.Show(
                        $"تم تصدير {projectsToExport.Count} مشروع إلى Excel بنجاح!\n\nهل تريد فتح الملف الآن؟",
                        "تصدير ناجح",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Information);

                    // فتح الملف إذا طلب المستخدم ذلك
                    if (result == MessageBoxResult.Yes)
                    {
                        try
                        {
                            System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                            {
                                FileName = saveFileDialog.FileName,
                                UseShellExecute = true
                            });
                        }
                        catch (Exception openEx)
                        {
                            MessageBox.Show($"تم حفظ الملف بنجاح ولكن لا يمكن فتحه تلقائياً: {openEx.Message}",
                                          "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // التحقق من رسالة CSV
                if (ex.Message.Contains("تم حفظ البيانات كملف CSV"))
                {
                    var result = MessageBox.Show(ex.Message + "\n\nهل تريد فتح الملف الآن؟",
                                                "تم التصدير كملف CSV",
                                                MessageBoxButton.YesNo,
                                                MessageBoxImage.Information);

                    if (result == MessageBoxResult.Yes)
                    {
                        try
                        {
                            var csvPath = ex.Message.Split(':')[1].Split('\n')[0].Trim();
                            System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                            {
                                FileName = csvPath,
                                UseShellExecute = true
                            });
                        }
                        catch
                        {
                            // تجاهل خطأ فتح الملف
                        }
                    }
                }
                else
                {
                    MessageBox.Show($"حدث خطأ أثناء التصدير: {ex.Message}",
                                  "خطأ في التصدير", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void SaveToDatabase()
        {
            try
            {
                // إعادة تحميل البيانات من قاعدة البيانات للتأكد من التحديث
                await _projectService.RefreshFromDatabaseAsync();
                RefreshProjects();

                MessageBox.Show("تم حفظ البيانات في قاعدة البيانات بنجاح!", "حفظ ناجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء الحفظ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SortProjects(object parameter)
        {
            if (parameter is string sortBy)
            {
                var direction = ListSortDirection.Ascending;

                // Check if already sorted by this property, then reverse direction
                if (_projectsView.SortDescriptions.Count > 0 &&
                    _projectsView.SortDescriptions[0].PropertyName == sortBy)
                {
                    direction = _projectsView.SortDescriptions[0].Direction == ListSortDirection.Ascending
                        ? ListSortDirection.Descending
                        : ListSortDirection.Ascending;
                }

                _projectsView.SortDescriptions.Clear();
                _projectsView.SortDescriptions.Add(new SortDescription(sortBy, direction));
            }
        }

        private void RefreshProjects()
        {
            Projects = _projectService.GetAllProjects();
            _projectsView = CollectionViewSource.GetDefaultView(_projects);
            _projectsView.Filter = ProjectFilter;
            OnPropertyChanged(nameof(ProjectsView));
        }

        private void DesignIPTT(object? parameter)
        {
            if (parameter is Project project)
            {
                try
                {
                    // إنشاء نافذة تصميم IPTT للمشروع المحدد
                    var ipttWindow = new Views.IPTT.IPTTDesignWindow(project);
                    ipttWindow.Owner = Application.Current.MainWindow;
                    ipttWindow.ShowDialog();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ أثناء فتح نافذة تصميم IPTT:\n{ex.Message}",
                                  "خطأ",
                                  MessageBoxButton.OK,
                                  MessageBoxImage.Error);
                }
            }
        }

        #region New Enhanced Methods

        /// <summary>
        /// تحميل ملخصات المشاريع مع بيانات IPTT
        /// </summary>
        private async Task LoadProjectSummariesAsync()
        {
            try
            {
                ProjectSummaries.Clear();

                foreach (var project in Projects)
                {
                    var summary = new ProjectSummary(project);

                    // تحميل بيانات IPTT للمشروع
                    try
                    {
                        var ipttData = await _ipttDatabaseService.LoadIpttDataAsync(project.Id.ToString());
                        if (ipttData != null)
                        {
                            var totalIndicators = ipttData.Indicators.Count;
                            var completedIndicators = CalculateCompletedIndicators(ipttData.Indicators);
                            var locationCount = ipttData.LocationData.Count;
                            var completionPercentage = totalIndicators > 0 ? (double)completedIndicators / totalIndicators * 100 : 0;

                            summary.UpdateFromIpttData(totalIndicators, completedIndicators, locationCount, completionPercentage);
                        }
                    }
                    catch
                    {
                        // في حالة عدم وجود بيانات IPTT، استخدم القيم الافتراضية
                        summary.UpdateFromIpttData(0, 0, 0, 0);
                    }

                    ProjectSummaries.Add(summary);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل ملخصات المشاريع: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حساب عدد المؤشرات المكتملة
        /// </summary>
        private int CalculateCompletedIndicators(ObservableCollection<Models.IPTT.IpttIndicator> indicators)
        {
            int completed = 0;
            foreach (var indicator in indicators)
            {
                // اعتبار المؤشر مكتمل إذا كان له قيم في الأشهر
                if (indicator.MonthlyValues.Any(mv => !string.IsNullOrEmpty(mv.Value)))
                {
                    completed++;
                }
            }
            return completed;
        }

        /// <summary>
        /// فتح IPTT مدمج داخل واجهة المشاريع
        /// </summary>
        private void OpenEmbeddedIPTT(object? parameter)
        {
            if (parameter is Project project)
            {
                try
                {
                    // هذه الطريقة ستطور لاحقاً لفتح IPTT مدمج
                    // حالياً ستفتح النافذة العادية
                    DesignIPTT(parameter);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح المؤشرات: {ex.Message}", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// التحقق من إمكانية فتح IPTT
        /// </summary>
        private bool CanOpenIPTT(object? parameter)
        {
            return parameter is Project;
        }

        /// <summary>
        /// مسح جميع الفلاتر
        /// </summary>
        private void ClearFilters()
        {
            SearchText = string.Empty;
            SelectedStatusFilter = "الكل";
            SelectedRegionFilter = "الكل";
            SelectedDonorFilter = "الكل";
            StartDateFilter = null;
            EndDateFilter = null;
        }

        #endregion
    }
}
