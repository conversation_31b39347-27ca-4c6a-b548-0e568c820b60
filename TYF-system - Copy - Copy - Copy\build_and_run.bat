@echo off
echo ========================================
echo بناء وتشغيل نظام TYF Management System
echo ========================================

echo المسار الحالي: %CD%
echo مسار الملف: %~dp0

cd /d "%~dp0"
echo تم الانتقال إلى: %CD%

if not exist "TYFManagementSystem" (
    echo ❌ مجلد TYFManagementSystem غير موجود!
    echo المحتويات الحالية:
    dir
    pause
    exit /b 1
)

cd TYFManagementSystem
echo تم الانتقال إلى مجلد المشروع: %CD%

echo.
echo 🔧 تنظيف المشروع...
dotnet clean

echo.
echo 📦 استعادة الحزم...
dotnet restore

echo.
echo 🏗️ بناء المشروع...
dotnet build --configuration Release

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ فشل في بناء المشروع!
    echo تحقق من الأخطاء أعلاه وحاول مرة أخرى.
    pause
    exit /b 1
)

echo.
echo ✅ تم بناء المشروع بنجاح!
echo.
echo 🚀 تشغيل التطبيق...
echo.

dotnet run

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ فشل في تشغيل التطبيق!
    pause
    exit /b 1
)

echo.
echo ✅ تم إغلاق التطبيق بنجاح.
pause
