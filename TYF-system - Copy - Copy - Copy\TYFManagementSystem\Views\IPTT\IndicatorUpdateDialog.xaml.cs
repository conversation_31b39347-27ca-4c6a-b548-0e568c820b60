using System;
using System.Windows;
using TYFManagementSystem.Models.IPTT;

namespace TYFManagementSystem.Views.IPTT
{
    public partial class IndicatorUpdateDialog : Window
    {
        private PerformanceIndicator _indicator;

        public IndicatorUpdateDialog(PerformanceIndicator indicator)
        {
            InitializeComponent();
            _indicator = indicator;
            LoadIndicatorData();
        }

        private void LoadIndicatorData()
        {
            IndicatorNumberTextBox.Text = _indicator.IndicatorNumber;
            IndicatorNameTextBox.Text = _indicator.Name;
            UnitTextBox.Text = _indicator.Unit;
            TargetValueTextBox.Text = _indicator.TargetValue.ToString();
            CurrentValueTextBox.Text = _indicator.CurrentValue.ToString();
            DescriptionTextBox.Text = _indicator.Description;
            DataSourceTextBox.Text = _indicator.DataSource;
            ResponsiblePersonTextBox.Text = _indicator.ResponsiblePerson;
        }

        private void Save_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(IndicatorNameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المؤشر", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    IndicatorNameTextBox.Focus();
                    return;
                }

                if (!double.TryParse(TargetValueTextBox.Text, out double targetValue))
                {
                    MessageBox.Show("يرجى إدخال قيمة مستهدفة صحيحة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    TargetValueTextBox.Focus();
                    return;
                }

                if (!double.TryParse(CurrentValueTextBox.Text, out double currentValue))
                {
                    MessageBox.Show("يرجى إدخال قيمة حالية صحيحة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    CurrentValueTextBox.Focus();
                    return;
                }

                // تحديث بيانات المؤشر
                _indicator.IndicatorNumber = IndicatorNumberTextBox.Text.Trim();
                _indicator.Name = IndicatorNameTextBox.Text.Trim();
                _indicator.Unit = UnitTextBox.Text.Trim();
                _indicator.TargetValue = targetValue;
                _indicator.CurrentValue = currentValue;
                _indicator.Description = DescriptionTextBox.Text.Trim();
                _indicator.DataSource = DataSourceTextBox.Text.Trim();
                _indicator.ResponsiblePerson = ResponsiblePersonTextBox.Text.Trim();

                // تحديث النسبة المئوية والحالة
                _indicator.UpdatePercentage();

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات:\n{ex.Message}", 
                              "خطأ", 
                              MessageBoxButton.OK, 
                              MessageBoxImage.Error);
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
