using System;
using System.Threading.Tasks;
using System.Windows.Threading;
using TYFManagementSystem.Models;
using TYFManagementSystem.Models.IPTT;
using System.Collections.ObjectModel;
using System.Collections.Generic;
using System.Linq;

namespace TYFManagementSystem.Services.IPTT
{
    /// <summary>
    /// خدمة الحفظ التلقائي والفوري لبيانات IPTT
    /// </summary>
    public class AutoSaveService : IDisposable
    {
        #region Private Fields
        private readonly IpttDatabaseService _databaseService;
        private readonly DispatcherTimer _autoSaveTimer;
        private readonly Dictionary<string, PendingSave> _pendingSaves;
        private readonly object _lockObject = new object();
        private bool _isDisposed = false;
        #endregion

        #region Constructor
        public AutoSaveService()
        {
            _databaseService = new IpttDatabaseService();
            _pendingSaves = new Dictionary<string, PendingSave>();
            
            // إعداد مؤقت الحفظ التلقائي
            _autoSaveTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMinutes(1) // حفظ تلقائي كل دقيقة
            };
            _autoSaveTimer.Tick += AutoSaveTimer_Tick;
            _autoSaveTimer.Start();
        }
        #endregion

        #region Events
        public event EventHandler<AutoSaveEventArgs> AutoSaveCompleted;
        public event EventHandler<AutoSaveEventArgs> AutoSaveFailed;
        #endregion

        #region Public Methods
        /// <summary>
        /// حفظ فوري لتغيير واحد في خلية
        /// </summary>
        public async Task SaveCellChangeAsync(Project project, int locationNumber, string indicatorNo, 
                                            string monthColumn, string newValue)
        {
            try
            {
                var saveKey = $"{project.Id}_{locationNumber}_{indicatorNo}_{monthColumn}";
                
                lock (_lockObject)
                {
                    _pendingSaves[saveKey] = new PendingSave
                    {
                        Project = project,
                        LocationNumber = locationNumber,
                        IndicatorNo = indicatorNo,
                        MonthColumn = monthColumn,
                        NewValue = newValue,
                        Timestamp = DateTime.Now,
                        SaveType = SaveType.CellChange
                    };
                }

                // حفظ فوري
                await ProcessPendingSave(saveKey);
            }
            catch (Exception ex)
            {
                OnAutoSaveFailed(new AutoSaveEventArgs
                {
                    Project = project,
                    ErrorMessage = $"فشل في الحفظ الفوري: {ex.Message}",
                    SaveType = SaveType.CellChange
                });
            }
        }

        /// <summary>
        /// حفظ فوري لموقع كامل
        /// </summary>
        public async Task SaveLocationAsync(Project project, int locationNumber, 
                                          ObservableCollection<IpttDisplayRow> locationData,
                                          List<string> monthColumns)
        {
            try
            {
                var saveKey = $"{project.Id}_location_{locationNumber}";
                
                lock (_lockObject)
                {
                    _pendingSaves[saveKey] = new PendingSave
                    {
                        Project = project,
                        LocationNumber = locationNumber,
                        LocationData = new ObservableCollection<IpttDisplayRow>(locationData),
                        MonthColumns = new List<string>(monthColumns),
                        Timestamp = DateTime.Now,
                        SaveType = SaveType.LocationData
                    };
                }

                // حفظ فوري
                await ProcessPendingSave(saveKey);
            }
            catch (Exception ex)
            {
                OnAutoSaveFailed(new AutoSaveEventArgs
                {
                    Project = project,
                    ErrorMessage = $"فشل في حفظ الموقع: {ex.Message}",
                    SaveType = SaveType.LocationData
                });
            }
        }

        /// <summary>
        /// حفظ مشروع كامل
        /// </summary>
        public async Task SaveProjectAsync(Project project, 
                                         ObservableCollection<IpttIndicator> indicators,
                                         List<string> monthColumns,
                                         Dictionary<int, ObservableCollection<IpttDisplayRow>> locationData)
        {
            try
            {
                var saveKey = $"{project.Id}_full_project";
                
                lock (_lockObject)
                {
                    _pendingSaves[saveKey] = new PendingSave
                    {
                        Project = project,
                        Indicators = new ObservableCollection<IpttIndicator>(indicators),
                        MonthColumns = new List<string>(monthColumns),
                        AllLocationData = new Dictionary<int, ObservableCollection<IpttDisplayRow>>(locationData),
                        Timestamp = DateTime.Now,
                        SaveType = SaveType.FullProject
                    };
                }

                // حفظ فوري
                await ProcessPendingSave(saveKey);
            }
            catch (Exception ex)
            {
                OnAutoSaveFailed(new AutoSaveEventArgs
                {
                    Project = project,
                    ErrorMessage = $"فشل في حفظ المشروع: {ex.Message}",
                    SaveType = SaveType.FullProject
                });
            }
        }

        /// <summary>
        /// تفعيل أو إيقاف الحفظ التلقائي
        /// </summary>
        public void SetAutoSaveEnabled(bool enabled)
        {
            if (enabled)
            {
                _autoSaveTimer.Start();
            }
            else
            {
                _autoSaveTimer.Stop();
            }
        }

        /// <summary>
        /// تغيير فترة الحفظ التلقائي
        /// </summary>
        public void SetAutoSaveInterval(TimeSpan interval)
        {
            _autoSaveTimer.Interval = interval;
        }
        #endregion

        #region Private Methods
        private async void AutoSaveTimer_Tick(object sender, EventArgs e)
        {
            await ProcessAllPendingSaves();
        }

        private async Task ProcessAllPendingSaves()
        {
            List<string> keysToProcess;
            
            lock (_lockObject)
            {
                keysToProcess = _pendingSaves.Keys.ToList();
            }

            foreach (var key in keysToProcess)
            {
                try
                {
                    await ProcessPendingSave(key);
                }
                catch (Exception ex)
                {
                    // تسجيل الخطأ ولكن لا نوقف المعالجة
                    System.Diagnostics.Debug.WriteLine($"خطأ في الحفظ التلقائي: {ex.Message}");
                }
            }
        }

        private async Task ProcessPendingSave(string saveKey)
        {
            PendingSave pendingSave;
            
            lock (_lockObject)
            {
                if (!_pendingSaves.TryGetValue(saveKey, out pendingSave))
                    return;
                
                _pendingSaves.Remove(saveKey);
            }

            try
            {
                switch (pendingSave.SaveType)
                {
                    case SaveType.CellChange:
                        await SaveSingleCell(pendingSave);
                        break;
                    
                    case SaveType.LocationData:
                        await SaveLocationData(pendingSave);
                        break;
                    
                    case SaveType.FullProject:
                        await SaveFullProject(pendingSave);
                        break;
                }

                OnAutoSaveCompleted(new AutoSaveEventArgs
                {
                    Project = pendingSave.Project,
                    SaveType = pendingSave.SaveType,
                    Message = "تم الحفظ بنجاح"
                });
            }
            catch (Exception ex)
            {
                OnAutoSaveFailed(new AutoSaveEventArgs
                {
                    Project = pendingSave.Project,
                    SaveType = pendingSave.SaveType,
                    ErrorMessage = ex.Message
                });
            }
        }

        private async Task SaveSingleCell(PendingSave pendingSave)
        {
            // تحميل البيانات الحالية
            var currentData = await _databaseService.LoadIpttDataAsync(pendingSave.Project.Id.ToString());
            if (currentData == null) return;

            // تحديث الخلية المحددة
            if (currentData.LocationData.ContainsKey(pendingSave.LocationNumber))
            {
                var locationData = currentData.LocationData[pendingSave.LocationNumber];
                var indicator = locationData.FirstOrDefault(r => r.No == pendingSave.IndicatorNo);
                
                if (indicator != null && indicator.MonthlyValues.ContainsKey(pendingSave.MonthColumn))
                {
                    indicator.MonthlyValues[pendingSave.MonthColumn] = pendingSave.NewValue;
                    
                    // حفظ البيانات المحدثة
                    await _databaseService.SaveIpttDataAsync(pendingSave.Project, currentData.Indicators, 
                                                           currentData.MonthColumns, currentData.LocationData);
                }
            }
        }

        private async Task SaveLocationData(PendingSave pendingSave)
        {
            // تحويل بيانات الموقع إلى مؤشرات
            var indicators = ConvertDisplayRowsToIndicators(pendingSave.LocationData);
            var locationData = new Dictionary<int, ObservableCollection<IpttDisplayRow>>
            {
                { pendingSave.LocationNumber, pendingSave.LocationData }
            };

            await _databaseService.SaveIpttDataAsync(pendingSave.Project, indicators, 
                                                   pendingSave.MonthColumns, locationData);
        }

        private async Task SaveFullProject(PendingSave pendingSave)
        {
            await _databaseService.SaveIpttDataAsync(pendingSave.Project, pendingSave.Indicators,
                                                   pendingSave.MonthColumns, pendingSave.AllLocationData);
        }

        private ObservableCollection<IpttIndicator> ConvertDisplayRowsToIndicators(ObservableCollection<IpttDisplayRow> rows)
        {
            var indicators = new ObservableCollection<IpttIndicator>();
            
            foreach (var row in rows.Where(r => r.IsIndicatorRow))
            {
                var indicator = new IpttIndicator
                {
                    No = row.No,
                    Indicator = row.Indicator,
                    DataType = row.DataType,
                    Target = row.Target,
                    MonthlyValues = new Dictionary<string, string>(row.MonthlyValues)
                };
                
                indicators.Add(indicator);
            }
            
            return indicators;
        }

        private void OnAutoSaveCompleted(AutoSaveEventArgs args)
        {
            AutoSaveCompleted?.Invoke(this, args);
        }

        private void OnAutoSaveFailed(AutoSaveEventArgs args)
        {
            AutoSaveFailed?.Invoke(this, args);
        }
        #endregion

        #region IDisposable Implementation
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_isDisposed && disposing)
            {
                _autoSaveTimer?.Stop();
                _autoSaveTimer?.Dispose();
                _isDisposed = true;
            }
        }
        #endregion
    }

    #region Supporting Classes
    /// <summary>
    /// حفظ معلق
    /// </summary>
    internal class PendingSave
    {
        public Project Project { get; set; }
        public int LocationNumber { get; set; }
        public string IndicatorNo { get; set; }
        public string MonthColumn { get; set; }
        public string NewValue { get; set; }
        public ObservableCollection<IpttDisplayRow> LocationData { get; set; }
        public ObservableCollection<IpttIndicator> Indicators { get; set; }
        public List<string> MonthColumns { get; set; }
        public Dictionary<int, ObservableCollection<IpttDisplayRow>> AllLocationData { get; set; }
        public DateTime Timestamp { get; set; }
        public SaveType SaveType { get; set; }
    }

    /// <summary>
    /// نوع الحفظ
    /// </summary>
    public enum SaveType
    {
        CellChange,
        LocationData,
        FullProject
    }

    /// <summary>
    /// معاملات حدث الحفظ التلقائي
    /// </summary>
    public class AutoSaveEventArgs : EventArgs
    {
        public Project Project { get; set; }
        public SaveType SaveType { get; set; }
        public string Message { get; set; }
        public string ErrorMessage { get; set; }
    }
    #endregion
}
