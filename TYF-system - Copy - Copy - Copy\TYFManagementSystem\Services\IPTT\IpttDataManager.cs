using System.Collections.ObjectModel;
using TYFManagementSystem.Models;
using TYFManagementSystem.Models.IPTT;

namespace TYFManagementSystem.Services.IPTT
{
    /// <summary>
    /// خدمة إدارة بيانات IPTT - مسؤولة عن إدارة البيانات الأساسية والمؤشرات
    /// </summary>
    public class IpttDataManager
    {
        private readonly IpttDatabaseService _databaseService;
        
        public IpttDataManager()
        {
            _databaseService = new IpttDatabaseService();
        }

        /// <summary>
        /// تحميل بيانات IPTT للمشروع
        /// </summary>
        public async Task<IpttDataModel?> LoadProjectDataAsync(int projectId)
        {
            try
            {
                var loadResult = await _databaseService.LoadIpttDataAsync(projectId.ToString());
                if (loadResult == null)
                    return null;

                return new IpttDataModel
                {
                    ProjectInfo = loadResult.ProjectInfo,
                    Indicators = loadResult.Indicators,
                    MonthColumns = loadResult.MonthColumns,
                    LocationData = loadResult.LocationData,
                    LocationCount = loadResult.LocationCount
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في تحميل بيانات المشروع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حفظ بيانات IPTT للمشروع
        /// </summary>
        public async Task<bool> SaveProjectDataAsync(IpttDataModel dataModel)
        {
            try
            {
                return await _databaseService.SaveIpttDataAsync(
                    dataModel.ProjectInfo,
                    dataModel.Indicators,
                    dataModel.MonthColumns,
                    dataModel.LocationData
                );
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في حفظ بيانات المشروع: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إنشاء مؤشر جديد
        /// </summary>
        public IpttIndicator CreateNewIndicator(string number, string name, List<DataTypeItem> dataTypes)
        {
            return new IpttIndicator
            {
                No = number,
                Indicator = name,
                DataTypes = new ObservableCollection<DataTypeItem>(dataTypes)
            };
        }

        /// <summary>
        /// إنشاء نوع بيانات جديد
        /// </summary>
        public DataTypeItem CreateNewDataType(string name, string target)
        {
            return new DataTypeItem
            {
                Name = name,
                Target = target,
                MonthlyValues = new Dictionary<string, string>()
            };
        }

        /// <summary>
        /// تحويل المؤشرات إلى صفوف عرض
        /// </summary>
        public ObservableCollection<IpttDisplayRow> ConvertIndicatorsToDisplayRows(
            ObservableCollection<IpttIndicator> indicators, 
            List<string> monthColumns)
        {
            var displayRows = new ObservableCollection<IpttDisplayRow>();

            foreach (var indicator in indicators)
            {
                // إضافة صف المؤشر الرئيسي
                var mainRow = CreateMainIndicatorRow(indicator, monthColumns);
                displayRows.Add(mainRow);

                // إضافة صفوف أنواع البيانات
                foreach (var dataType in indicator.DataTypes)
                {
                    var dataTypeRow = CreateDataTypeRow(indicator, dataType, monthColumns);
                    displayRows.Add(dataTypeRow);
                }
            }

            return displayRows;
        }

        /// <summary>
        /// إنشاء صف المؤشر الرئيسي
        /// </summary>
        private IpttDisplayRow CreateMainIndicatorRow(IpttIndicator indicator, List<string> monthColumns)
        {
            var mainRow = new IpttDisplayRow
            {
                No = indicator.No,
                Indicator = indicator.Indicator,
                DataType = "",
                Target = "",
                Achievement = "",
                AchievementPercentage = "",
                IsMainIndicator = true,
                IsDataTypeRow = false,
                IsTotalRow = false,
                IsCompleted = false,
                CanDelete = true,
                IndicatorId = indicator.No,
                MonthlyData = new Dictionary<string, string>()
            };

            // تهيئة البيانات الشهرية
            foreach (var month in monthColumns)
            {
                mainRow.MonthlyData[month] = "";
            }

            return mainRow;
        }

        /// <summary>
        /// إنشاء صف نوع البيانات
        /// </summary>
        private IpttDisplayRow CreateDataTypeRow(IpttIndicator indicator, DataTypeItem dataType, List<string> monthColumns)
        {
            var dataTypeRow = new IpttDisplayRow
            {
                No = "",
                Indicator = "",
                DataType = dataType.Name,
                Target = dataType.Target,
                Achievement = "",
                AchievementPercentage = "",
                IsMainIndicator = false,
                IsDataTypeRow = true,
                IsTotalRow = false,
                IsCompleted = false,
                CanDelete = true,
                IndicatorId = indicator.No,
                MonthlyData = new Dictionary<string, string>()
            };

            // نسخ البيانات الشهرية
            foreach (var month in monthColumns)
            {
                if (dataType.MonthlyValues.ContainsKey(month))
                {
                    dataTypeRow.MonthlyData[month] = dataType.MonthlyValues[month];
                }
                else
                {
                    dataTypeRow.MonthlyData[month] = "";
                }
            }

            return dataTypeRow;
        }

        /// <summary>
        /// تحديث البيانات الأساسية من صفوف العرض
        /// </summary>
        public void UpdateIndicatorsFromDisplayRows(
            ObservableCollection<IpttIndicator> indicators,
            ObservableCollection<IpttDisplayRow> displayRows)
        {
            foreach (var indicator in indicators)
            {
                foreach (var dataType in indicator.DataTypes)
                {
                    var dataTypeRow = displayRows.FirstOrDefault(r => 
                        r.IndicatorId == indicator.No && 
                        r.DataType == dataType.Name && 
                        r.IsDataTypeRow);

                    if (dataTypeRow != null)
                    {
                        // تحديث البيانات الشهرية
                        dataType.MonthlyValues.Clear();
                        foreach (var monthData in dataTypeRow.MonthlyData)
                        {
                            if (!string.IsNullOrWhiteSpace(monthData.Value))
                            {
                                dataType.MonthlyValues[monthData.Key] = monthData.Value;
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// إنشاء أعمدة الأشهر بناءً على مدة المشروع
        /// </summary>
        public List<string> GenerateMonthColumns(DateTime startDate, DateTime endDate)
        {
            var monthColumns = new List<string>();
            var current = new DateTime(startDate.Year, startDate.Month, 1);
            var end = new DateTime(endDate.Year, endDate.Month, 1);

            while (current <= end)
            {
                monthColumns.Add(current.ToString("yyyy/MM"));
                current = current.AddMonths(1);
            }

            return monthColumns;
        }

        /// <summary>
        /// التحقق من وجود بيانات للمشروع
        /// </summary>
        public async Task<bool> HasProjectDataAsync(int projectId)
        {
            return await _databaseService.HasIpttDataAsync(projectId.ToString());
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        public void Dispose()
        {
            _databaseService?.Dispose();
        }
    }

    /// <summary>
    /// نموذج بيانات IPTT الموحد
    /// </summary>
    public class IpttDataModel
    {
        public Project ProjectInfo { get; set; } = new Project();
        public ObservableCollection<IpttIndicator> Indicators { get; set; } = new ObservableCollection<IpttIndicator>();
        public List<string> MonthColumns { get; set; } = new List<string>();
        public Dictionary<int, ObservableCollection<IpttDisplayRow>> LocationData { get; set; } = new Dictionary<int, ObservableCollection<IpttDisplayRow>>();
        public int LocationCount { get; set; } = 1;
    }
}
