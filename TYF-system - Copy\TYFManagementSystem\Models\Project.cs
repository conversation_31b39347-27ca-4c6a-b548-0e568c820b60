using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace TYFManagementSystem.Models
{
    public class Project : INotifyPropertyChanged
    {
        private int _id;
        private string _projectNumber = string.Empty;
        private string _projectCode = string.Empty;
        private string _name = string.Empty;
        private string _region = string.Empty;
        private string _description = string.Empty;
        private DateTime _startDate;
        private DateTime _endDate;
        private string _status = string.Empty;
        private decimal _budget;
        private string _manager = string.Empty;
        private int _beneficiaries;

        public int Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        public string ProjectNumber
        {
            get => _projectNumber;
            set => SetProperty(ref _projectNumber, value);
        }

        public string ProjectCode
        {
            get => _projectCode;
            set => SetProperty(ref _projectCode, value);
        }

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public string Region
        {
            get => _region;
            set => SetProperty(ref _region, value);
        }

        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        public DateTime StartDate
        {
            get => _startDate;
            set => SetProperty(ref _startDate, value);
        }

        public DateTime EndDate
        {
            get => _endDate;
            set => SetProperty(ref _endDate, value);
        }

        public string Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        public decimal Budget
        {
            get => _budget;
            set => SetProperty(ref _budget, value);
        }

        public string Manager
        {
            get => _manager;
            set => SetProperty(ref _manager, value);
        }

        public int Beneficiaries
        {
            get => _beneficiaries;
            set => SetProperty(ref _beneficiaries, value);
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected virtual bool SetProperty<T>(ref T storage, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(storage, value))
                return false;

            storage = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}
