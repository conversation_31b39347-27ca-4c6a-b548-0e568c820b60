using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace TYFManagementSystem.Models
{
    public class Project : INotifyPropertyChanged
    {
        private int _id;
        private string _projectNumber = string.Empty;
        private string _projectCode = string.Empty;
        private string _name = string.Empty;
        private string _region = string.Empty;
        private string _description = string.Empty;
        private DateTime _startDate;
        private DateTime _endDate;
        private string _status = string.Empty;
        private decimal _budget;
        private string _manager = string.Empty;
        private int _beneficiaries;
        private string _donor = string.Empty;
        private double _completionPercentage;
        private int _locationCount;

        public int Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        public string ProjectNumber
        {
            get => _projectNumber;
            set => SetProperty(ref _projectNumber, value);
        }

        public string ProjectCode
        {
            get => _projectCode;
            set => SetProperty(ref _projectCode, value);
        }

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public string Region
        {
            get => _region;
            set => SetProperty(ref _region, value);
        }

        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        public DateTime StartDate
        {
            get => _startDate;
            set => SetProperty(ref _startDate, value);
        }

        public DateTime EndDate
        {
            get => _endDate;
            set => SetProperty(ref _endDate, value);
        }

        public string Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        public decimal Budget
        {
            get => _budget;
            set => SetProperty(ref _budget, value);
        }

        public string Manager
        {
            get => _manager;
            set => SetProperty(ref _manager, value);
        }

        public int Beneficiaries
        {
            get => _beneficiaries;
            set => SetProperty(ref _beneficiaries, value);
        }

        public string Donor
        {
            get => _donor;
            set => SetProperty(ref _donor, value);
        }

        public double CompletionPercentage
        {
            get => _completionPercentage;
            set => SetProperty(ref _completionPercentage, value);
        }

        public int LocationCount
        {
            get => _locationCount;
            set => SetProperty(ref _locationCount, value);
        }

        // Computed properties for summary
        public string StatusColor => Status switch
        {
            "نشط" => "#4CAF50",
            "مخطط" => "#FF9800",
            "مكتمل" => "#2196F3",
            "متوقف" => "#F44336",
            "مؤجل" => "#9C27B0",
            _ => "#757575"
        };

        public string CompletionStatus => CompletionPercentage switch
        {
            >= 100 => "مكتمل",
            >= 75 => "متقدم",
            >= 50 => "متوسط",
            >= 25 => "بداية",
            > 0 => "بدء",
            _ => "لم يبدأ"
        };

        public TimeSpan RemainingTime => EndDate - DateTime.Now;

        public string RemainingTimeText
        {
            get
            {
                var remaining = RemainingTime;
                if (remaining.TotalDays < 0)
                    return "منتهي";
                else if (remaining.TotalDays < 30)
                    return $"{remaining.Days} يوم";
                else if (remaining.TotalDays < 365)
                    return $"{remaining.Days / 30} شهر";
                else
                    return $"{remaining.Days / 365} سنة";
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected virtual bool SetProperty<T>(ref T storage, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(storage, value))
                return false;

            storage = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}
