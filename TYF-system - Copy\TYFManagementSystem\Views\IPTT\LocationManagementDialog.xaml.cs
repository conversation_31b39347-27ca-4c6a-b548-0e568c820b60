using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace TYFManagementSystem.Views.IPTT
{
    /// <summary>
    /// Interaction logic for LocationManagementDialog.xaml
    /// </summary>
    public partial class LocationManagementDialog : Window
    {
        public LocationManagementDialog()
        {
            InitializeComponent();
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // تركيز على حقل العدد الجديد
            NewLocationCountTextBox.Focus();
            NewLocationCountTextBox.SelectAll();
        }
    }

    #region Value Converters
    /// <summary>
    /// محول عكسي للقيم المنطقية إلى الرؤية
    /// </summary>
    public class InverseBoolToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Collapsed : Visibility.Visible;
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
    #endregion
}
